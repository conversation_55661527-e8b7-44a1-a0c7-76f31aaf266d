const { query } = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
  // 创建新用户
  static async create(userData) {
    const { username, email, password, fullName, phone, studentId } = userData;
    
    // 密码加密
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    const result = await query(
      `INSERT INTO users (username, email, password_hash, full_name, phone, student_id)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING id, username, email, full_name, phone, student_id, created_at`,
      [username, email, passwordHash, fullName, phone, studentId]
    );
    
    return result.rows[0];
  }

  // 根据用户名或邮箱查找用户
  static async findByUsernameOrEmail(identifier) {
    const result = await query(
      `SELECT * FROM users WHERE username = $1 OR email = $1`,
      [identifier]
    );
    return result.rows[0];
  }

  // 根据ID查找用户
  static async findById(id) {
    const result = await query(
      `SELECT id, username, email, full_name, phone, student_id, avatar_url, created_at
       FROM users WHERE id = $1`,
      [id]
    );
    return result.rows[0];
  }

  // 验证密码
  static async validatePassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  // 更新用户信息
  static async update(id, updateData) {
    const fields = [];
    const values = [];
    let paramCount = 1;

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = $${paramCount}`);
        values.push(updateData[key]);
        paramCount++;
      }
    });

    if (fields.length === 0) {
      throw new Error('没有要更新的字段');
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const result = await query(
      `UPDATE users SET ${fields.join(', ')} WHERE id = $${paramCount}
       RETURNING id, username, email, full_name, phone, student_id, avatar_url, updated_at`,
      values
    );

    return result.rows[0];
  }

  // 检查用户名是否存在
  static async checkUsernameExists(username) {
    const result = await query(
      `SELECT id FROM users WHERE username = $1`,
      [username]
    );
    return result.rows.length > 0;
  }

  // 检查邮箱是否存在
  static async checkEmailExists(email) {
    const result = await query(
      `SELECT id FROM users WHERE email = $1`,
      [email]
    );
    return result.rows.length > 0;
  }
}

module.exports = User;
