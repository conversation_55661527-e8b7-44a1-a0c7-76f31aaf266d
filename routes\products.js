const express = require('express');
const Product = require('../models/Product');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { upload, handleUploadError } = require('../middleware/upload');

const router = express.Router();

// 获取所有产品（支持分页和筛选）
router.get('/', optionalAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const filters = {
      category: req.query.category,
      search: req.query.search,
      minPrice: req.query.minPrice ? parseFloat(req.query.minPrice) : null,
      maxPrice: req.query.maxPrice ? parseFloat(req.query.maxPrice) : null
    };

    const result = await Product.findAll(page, limit, filters);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('获取产品列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取产品详情
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const productId = parseInt(req.params.id);
    
    if (isNaN(productId)) {
      return res.status(400).json({
        success: false,
        message: '无效的产品ID'
      });
    }

    const product = await Product.findById(productId);
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: '产品不存在'
      });
    }

    // 增加浏览次数
    await Product.incrementViewCount(productId);

    res.json({
      success: true,
      data: { product }
    });

  } catch (error) {
    console.error('获取产品详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 创建新产品
router.post('/', authenticateToken, upload.array('images', 5), handleUploadError, async (req, res) => {
  try {
    const { title, description, price, condition, categoryId } = req.body;

    // 验证必填字段
    if (!title || !description || !price) {
      return res.status(400).json({
        success: false,
        message: '请填写所有必填字段'
      });
    }

    // 验证价格
    const numPrice = parseFloat(price);
    if (isNaN(numPrice) || numPrice <= 0) {
      return res.status(400).json({
        success: false,
        message: '请输入有效的价格'
      });
    }

    // 创建产品
    const product = await Product.create({
      title,
      description,
      price: numPrice,
      condition: condition || '良好',
      categoryId: categoryId ? parseInt(categoryId) : null,
      sellerId: req.user.id
    });

    // 处理上传的图片
    if (req.files && req.files.length > 0) {
      for (let i = 0; i < req.files.length; i++) {
        const file = req.files[i];
        const imageUrl = `/uploads/${file.filename}`;
        const isPrimary = i === 0; // 第一张图片设为主图
        
        await Product.addImage(product.id, imageUrl, isPrimary);
      }
    }

    // 获取完整的产品信息
    const fullProduct = await Product.findById(product.id);

    res.status(201).json({
      success: true,
      message: '产品发布成功',
      data: { product: fullProduct }
    });

  } catch (error) {
    console.error('创建产品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 更新产品
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const productId = parseInt(req.params.id);
    const { title, description, price, condition, status } = req.body;

    if (isNaN(productId)) {
      return res.status(400).json({
        success: false,
        message: '无效的产品ID'
      });
    }

    // 检查产品是否存在且属于当前用户
    const existingProduct = await Product.findById(productId);
    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: '产品不存在'
      });
    }

    if (existingProduct.seller_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '无权限修改此产品'
      });
    }

    // 准备更新数据
    const updateData = {};
    if (title) updateData.title = title;
    if (description) updateData.description = description;
    if (price) {
      const numPrice = parseFloat(price);
      if (isNaN(numPrice) || numPrice <= 0) {
        return res.status(400).json({
          success: false,
          message: '请输入有效的价格'
        });
      }
      updateData.price = numPrice;
    }
    if (condition) updateData.condition = condition;
    if (status) updateData.status = status;

    // 更新产品
    const updatedProduct = await Product.update(productId, updateData);

    res.json({
      success: true,
      message: '产品更新成功',
      data: { product: updatedProduct }
    });

  } catch (error) {
    console.error('更新产品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 删除产品
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const productId = parseInt(req.params.id);

    if (isNaN(productId)) {
      return res.status(400).json({
        success: false,
        message: '无效的产品ID'
      });
    }

    // 检查产品是否存在且属于当前用户
    const existingProduct = await Product.findById(productId);
    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: '产品不存在'
      });
    }

    if (existingProduct.seller_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '无权限删除此产品'
      });
    }

    // 删除产品
    await Product.delete(productId);

    res.json({
      success: true,
      message: '产品删除成功'
    });

  } catch (error) {
    console.error('删除产品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取当前用户的产品
router.get('/user/my-products', authenticateToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;

    const products = await Product.findByUserId(req.user.id, page, limit);

    res.json({
      success: true,
      data: { products }
    });

  } catch (error) {
    console.error('获取用户产品错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取分类列表
router.get('/categories/list', async (req, res) => {
  try {
    const categories = await Product.getCategories();

    res.json({
      success: true,
      data: { categories }
    });

  } catch (error) {
    console.error('获取分类列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
