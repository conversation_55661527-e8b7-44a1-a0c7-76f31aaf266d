const { query } = require('../config/database');

class Favorite {
    // 添加收藏
    static async add(userId, productId) {
        try {
            const result = await query(
                `INSERT INTO favorites (user_id, product_id)
                 VALUES ($1, $2)
                 ON CONFLICT (user_id, product_id) DO NOTHING
                 RETURNING *`,
                [userId, productId]
            );
            return result.rows[0];
        } catch (error) {
            console.error('添加收藏失败:', error);
            throw error;
        }
    }

    // 取消收藏
    static async remove(userId, productId) {
        try {
            const result = await query(
                `DELETE FROM favorites 
                 WHERE user_id = $1 AND product_id = $2
                 RETURNING *`,
                [userId, productId]
            );
            return result.rows[0];
        } catch (error) {
            console.error('取消收藏失败:', error);
            throw error;
        }
    }

    // 检查是否已收藏
    static async isFavorited(userId, productId) {
        try {
            const result = await query(
                `SELECT id FROM favorites 
                 WHERE user_id = $1 AND product_id = $2`,
                [userId, productId]
            );
            return result.rows.length > 0;
        } catch (error) {
            console.error('检查收藏状态失败:', error);
            throw error;
        }
    }

    // 获取商品的收藏数量
    static async getProductFavoriteCount(productId) {
        try {
            const result = await query(
                `SELECT COUNT(*) as count FROM favorites 
                 WHERE product_id = $1`,
                [productId]
            );
            return parseInt(result.rows[0].count);
        } catch (error) {
            console.error('获取收藏数量失败:', error);
            throw error;
        }
    }

    // 获取用户的收藏列表
    static async getUserFavorites(userId, page = 1, limit = 12) {
        try {
            const offset = (page - 1) * limit;
            
            const result = await query(
                `SELECT p.*, u.username as seller_name, c.name as category_name,
                        pi.image_url as primary_image, f.created_at as favorited_at
                 FROM favorites f
                 JOIN products p ON f.product_id = p.id
                 LEFT JOIN users u ON p.seller_id = u.id
                 LEFT JOIN categories c ON p.category_id = c.id
                 LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = true
                 WHERE f.user_id = $1 AND p.status = '在售'
                 ORDER BY f.created_at DESC
                 LIMIT $2 OFFSET $3`,
                [userId, limit, offset]
            );

            // 获取总数
            const countResult = await query(
                `SELECT COUNT(*) FROM favorites f
                 JOIN products p ON f.product_id = p.id
                 WHERE f.user_id = $1 AND p.status = '在售'`,
                [userId]
            );

            return {
                favorites: result.rows,
                total: parseInt(countResult.rows[0].count),
                page,
                totalPages: Math.ceil(countResult.rows[0].count / limit)
            };
        } catch (error) {
            console.error('获取用户收藏失败:', error);
            throw error;
        }
    }

    // 获取多个商品的收藏状态（批量查询）
    static async getProductsFavoriteStatus(userId, productIds) {
        try {
            if (!productIds || productIds.length === 0) {
                return {};
            }

            const placeholders = productIds.map((_, index) => `$${index + 2}`).join(',');
            const result = await query(
                `SELECT product_id FROM favorites 
                 WHERE user_id = $1 AND product_id IN (${placeholders})`,
                [userId, ...productIds]
            );

            const favoriteStatus = {};
            productIds.forEach(id => {
                favoriteStatus[id] = false;
            });

            result.rows.forEach(row => {
                favoriteStatus[row.product_id] = true;
            });

            return favoriteStatus;
        } catch (error) {
            console.error('批量获取收藏状态失败:', error);
            throw error;
        }
    }

    // 获取商品的收藏用户列表
    static async getProductFavoriteUsers(productId, page = 1, limit = 20) {
        try {
            const offset = (page - 1) * limit;
            
            const result = await query(
                `SELECT u.id, u.username, u.full_name, f.created_at as favorited_at
                 FROM favorites f
                 JOIN users u ON f.user_id = u.id
                 WHERE f.product_id = $1
                 ORDER BY f.created_at DESC
                 LIMIT $2 OFFSET $3`,
                [productId, limit, offset]
            );

            return result.rows;
        } catch (error) {
            console.error('获取商品收藏用户失败:', error);
            throw error;
        }
    }

    // 获取用户收藏统计
    static async getUserFavoriteStats(userId) {
        try {
            const result = await query(
                `SELECT 
                    COUNT(*) as total_favorites,
                    COUNT(CASE WHEN p.status = '在售' THEN 1 END) as active_favorites,
                    COUNT(CASE WHEN p.status = '已售出' THEN 1 END) as sold_favorites
                 FROM favorites f
                 JOIN products p ON f.product_id = p.id
                 WHERE f.user_id = $1`,
                [userId]
            );

            return {
                totalFavorites: parseInt(result.rows[0].total_favorites),
                activeFavorites: parseInt(result.rows[0].active_favorites),
                soldFavorites: parseInt(result.rows[0].sold_favorites)
            };
        } catch (error) {
            console.error('获取用户收藏统计失败:', error);
            throw error;
        }
    }

    // 清理已删除商品的收藏记录（维护任务）
    static async cleanupDeletedProducts() {
        try {
            const result = await query(
                `DELETE FROM favorites 
                 WHERE product_id NOT IN (SELECT id FROM products)`
            );
            return result.rowCount;
        } catch (error) {
            console.error('清理收藏记录失败:', error);
            throw error;
        }
    }
}

module.exports = Favorite;
