const { query } = require('../config/database');

class Comment {
    // 创建留言
    static async create(commentData) {
        const { productId, userId, content } = commentData;
        
        try {
            const result = await query(
                `INSERT INTO comments (product_id, user_id, content)
                 VALUES ($1, $2, $3)
                 RETURNING *`,
                [productId, userId, content]
            );
            
            return result.rows[0];
        } catch (error) {
            console.error('创建留言失败:', error);
            throw error;
        }
    }

    // 获取商品的留言列表
    static async getByProductId(productId, page = 1, limit = 20) {
        try {
            const offset = (page - 1) * limit;
            
            const result = await query(
                `SELECT c.*, u.username, u.full_name
                 FROM comments c
                 JOIN users u ON c.user_id = u.id
                 WHERE c.product_id = $1
                 ORDER BY c.created_at DESC
                 LIMIT $2 OFFSET $3`,
                [productId, limit, offset]
            );

            // 获取总数
            const countResult = await query(
                `SELECT COUNT(*) FROM comments WHERE product_id = $1`,
                [productId]
            );

            return {
                comments: result.rows,
                total: parseInt(countResult.rows[0].count),
                page,
                totalPages: Math.ceil(countResult.rows[0].count / limit)
            };
        } catch (error) {
            console.error('获取留言列表失败:', error);
            throw error;
        }
    }

    // 根据ID获取留言
    static async findById(id) {
        try {
            const result = await query(
                `SELECT c.*, u.username, u.full_name
                 FROM comments c
                 JOIN users u ON c.user_id = u.id
                 WHERE c.id = $1`,
                [id]
            );
            
            return result.rows[0];
        } catch (error) {
            console.error('获取留言详情失败:', error);
            throw error;
        }
    }

    // 更新留言
    static async update(id, content) {
        try {
            const result = await query(
                `UPDATE comments 
                 SET content = $1, updated_at = CURRENT_TIMESTAMP
                 WHERE id = $2
                 RETURNING *`,
                [content, id]
            );
            
            return result.rows[0];
        } catch (error) {
            console.error('更新留言失败:', error);
            throw error;
        }
    }

    // 删除留言
    static async delete(id) {
        try {
            const result = await query(
                `DELETE FROM comments WHERE id = $1 RETURNING *`,
                [id]
            );
            
            return result.rows[0];
        } catch (error) {
            console.error('删除留言失败:', error);
            throw error;
        }
    }

    // 获取用户的留言列表
    static async getByUserId(userId, page = 1, limit = 20) {
        try {
            const offset = (page - 1) * limit;
            
            const result = await query(
                `SELECT c.*, p.title as product_title, p.id as product_id
                 FROM comments c
                 JOIN products p ON c.product_id = p.id
                 WHERE c.user_id = $1
                 ORDER BY c.created_at DESC
                 LIMIT $2 OFFSET $3`,
                [userId, limit, offset]
            );

            return result.rows;
        } catch (error) {
            console.error('获取用户留言失败:', error);
            throw error;
        }
    }

    // 获取商品的留言数量
    static async getProductCommentCount(productId) {
        try {
            const result = await query(
                `SELECT COUNT(*) as count FROM comments WHERE product_id = $1`,
                [productId]
            );
            
            return parseInt(result.rows[0].count);
        } catch (error) {
            console.error('获取留言数量失败:', error);
            throw error;
        }
    }

    // 检查用户是否可以删除留言（只能删除自己的留言）
    static async canUserDelete(commentId, userId) {
        try {
            const result = await query(
                `SELECT user_id FROM comments WHERE id = $1`,
                [commentId]
            );
            
            if (result.rows.length === 0) {
                return false;
            }
            
            return result.rows[0].user_id === userId;
        } catch (error) {
            console.error('检查删除权限失败:', error);
            throw error;
        }
    }

    // 获取留言统计信息
    static async getCommentStats(productId) {
        try {
            const result = await query(
                `SELECT 
                    COUNT(*) as total_comments,
                    COUNT(DISTINCT user_id) as unique_commenters,
                    MAX(created_at) as latest_comment_time
                 FROM comments 
                 WHERE product_id = $1`,
                [productId]
            );
            
            return {
                totalComments: parseInt(result.rows[0].total_comments),
                uniqueCommenters: parseInt(result.rows[0].unique_commenters),
                latestCommentTime: result.rows[0].latest_comment_time
            };
        } catch (error) {
            console.error('获取留言统计失败:', error);
            throw error;
        }
    }

    // 搜索留言内容
    static async search(keyword, page = 1, limit = 20) {
        try {
            const offset = (page - 1) * limit;
            
            const result = await query(
                `SELECT c.*, u.username, u.full_name, p.title as product_title
                 FROM comments c
                 JOIN users u ON c.user_id = u.id
                 JOIN products p ON c.product_id = p.id
                 WHERE c.content ILIKE $1
                 ORDER BY c.created_at DESC
                 LIMIT $2 OFFSET $3`,
                [`%${keyword}%`, limit, offset]
            );

            // 获取搜索结果总数
            const countResult = await query(
                `SELECT COUNT(*) FROM comments c
                 WHERE c.content ILIKE $1`,
                [`%${keyword}%`]
            );

            return {
                comments: result.rows,
                total: parseInt(countResult.rows[0].count),
                page,
                totalPages: Math.ceil(countResult.rows[0].count / limit)
            };
        } catch (error) {
            console.error('搜索留言失败:', error);
            throw error;
        }
    }

    // 清理已删除商品的留言记录（维护任务）
    static async cleanupDeletedProducts() {
        try {
            const result = await query(
                `DELETE FROM comments 
                 WHERE product_id NOT IN (SELECT id FROM products)`
            );
            return result.rowCount;
        } catch (error) {
            console.error('清理留言记录失败:', error);
            throw error;
        }
    }

    // 批量删除用户的所有留言
    static async deleteByUserId(userId) {
        try {
            const result = await query(
                `DELETE FROM comments WHERE user_id = $1`,
                [userId]
            );
            return result.rowCount;
        } catch (error) {
            console.error('批量删除用户留言失败:', error);
            throw error;
        }
    }
}

module.exports = Comment;
