// 国际化管理模块

class I18nManager {
    constructor() {
        this.currentLanguage = 'zh'; // 默认中文
        this.translations = {};
        this.supportedLanguages = ['zh', 'en'];
        this.init();
    }

    // 初始化国际化
    async init() {
        // 从localStorage获取用户语言偏好
        const savedLanguage = storage.get('language', 'zh');
        
        // 检查浏览器语言偏好
        const browserLanguage = this.getBrowserLanguage();
        
        // 确定使用的语言
        const language = this.supportedLanguages.includes(savedLanguage) 
            ? savedLanguage 
            : (this.supportedLanguages.includes(browserLanguage) ? browserLanguage : 'zh');

        await this.setLanguage(language);
        this.setupLanguageSelector();
    }

    // 获取浏览器语言
    getBrowserLanguage() {
        const language = navigator.language || navigator.userLanguage;
        return language.startsWith('en') ? 'en' : 'zh';
    }

    // 设置语言
    async setLanguage(language) {
        if (!this.supportedLanguages.includes(language)) {
            console.warn(`不支持的语言: ${language}`);
            return;
        }

        try {
            // 加载语言包
            await this.loadTranslations(language);
            
            this.currentLanguage = language;
            storage.set('language', language);
            
            // 更新页面文本
            this.updatePageTexts();
            
            // 更新语言选择器
            this.updateLanguageSelector();
            
            // 更新HTML lang属性
            document.documentElement.lang = language;
            
            console.log(`语言已切换到: ${language}`);
        } catch (error) {
            console.error('设置语言失败:', error);
            showToast('语言切换失败', 'error');
        }
    }

    // 加载翻译文件
    async loadTranslations(language) {
        if (this.translations[language]) {
            return; // 已加载
        }

        try {
            const response = await fetch(`/locales/${language}.json`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            this.translations[language] = await response.json();
        } catch (error) {
            console.error(`加载语言包失败 (${language}):`, error);
            throw error;
        }
    }

    // 获取翻译文本
    t(key, params = {}) {
        const keys = key.split('.');
        let value = this.translations[this.currentLanguage];
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                // 如果当前语言没有找到，尝试使用中文作为后备
                if (this.currentLanguage !== 'zh' && this.translations.zh) {
                    let fallback = this.translations.zh;
                    for (const fk of keys) {
                        if (fallback && typeof fallback === 'object' && fk in fallback) {
                            fallback = fallback[fk];
                        } else {
                            fallback = key; // 最终后备：返回key本身
                            break;
                        }
                    }
                    value = fallback;
                } else {
                    value = key; // 返回key本身作为后备
                }
                break;
            }
        }

        // 参数替换
        if (typeof value === 'string' && Object.keys(params).length > 0) {
            Object.keys(params).forEach(param => {
                value = value.replace(new RegExp(`{${param}}`, 'g'), params[param]);
            });
        }

        return value;
    }

    // 设置语言选择器
    setupLanguageSelector() {
        // 在导航栏添加语言切换按钮
        const navMenu = document.querySelector('.nav-menu');
        if (navMenu && !document.getElementById('languageSelector')) {
            const languageSelector = document.createElement('div');
            languageSelector.id = 'languageSelector';
            languageSelector.className = 'language-selector';
            languageSelector.innerHTML = `
                <button class="language-btn" id="currentLanguageBtn">
                    <span class="language-flag">${this.currentLanguage === 'zh' ? '🇨🇳' : '🇺🇸'}</span>
                    <span class="language-text">${this.currentLanguage === 'zh' ? '中文' : 'EN'}</span>
                    <span class="dropdown-arrow">▼</span>
                </button>
                <div class="language-dropdown" id="languageDropdown">
                    <button class="language-option" data-lang="zh">
                        <span class="language-flag">🇨🇳</span>
                        <span class="language-text">中文</span>
                    </button>
                    <button class="language-option" data-lang="en">
                        <span class="language-flag">🇺🇸</span>
                        <span class="language-text">English</span>
                    </button>
                </div>
            `;

            // 插入到用户菜单之前
            const userMenu = document.getElementById('userMenu');
            const authButtons = document.getElementById('authButtons');
            if (userMenu) {
                navMenu.insertBefore(languageSelector, userMenu);
            } else if (authButtons) {
                navMenu.insertBefore(languageSelector, authButtons);
            } else {
                navMenu.appendChild(languageSelector);
            }

            // 添加事件监听器
            this.setupLanguageSelectorEvents();
        }
    }

    // 设置语言选择器事件
    setupLanguageSelectorEvents() {
        const currentLanguageBtn = document.getElementById('currentLanguageBtn');
        const languageDropdown = document.getElementById('languageDropdown');
        const languageOptions = document.querySelectorAll('.language-option');

        if (currentLanguageBtn && languageDropdown) {
            // 点击按钮显示/隐藏下拉菜单
            currentLanguageBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                languageDropdown.classList.toggle('show');
            });

            // 点击其他地方隐藏下拉菜单
            document.addEventListener('click', () => {
                languageDropdown.classList.remove('show');
            });

            // 阻止下拉菜单点击事件冒泡
            languageDropdown.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // 语言选项点击事件
        languageOptions.forEach(option => {
            option.addEventListener('click', async (e) => {
                const selectedLang = e.currentTarget.dataset.lang;
                if (selectedLang !== this.currentLanguage) {
                    await this.setLanguage(selectedLang);
                }
                languageDropdown.classList.remove('show');
            });
        });
    }

    // 更新语言选择器显示
    updateLanguageSelector() {
        const currentLanguageBtn = document.getElementById('currentLanguageBtn');
        if (currentLanguageBtn) {
            const flag = currentLanguageBtn.querySelector('.language-flag');
            const text = currentLanguageBtn.querySelector('.language-text');
            
            if (flag && text) {
                flag.textContent = this.currentLanguage === 'zh' ? '🇨🇳' : '🇺🇸';
                text.textContent = this.currentLanguage === 'zh' ? '中文' : 'EN';
            }
        }
    }

    // 更新页面所有文本
    updatePageTexts() {
        // 更新所有带有data-i18n属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const text = this.t(key);
            
            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'email' || element.type === 'password' || element.type === 'number')) {
                element.placeholder = text;
            } else if (element.tagName === 'TEXTAREA') {
                element.placeholder = text;
            } else {
                element.textContent = text;
            }
        });

        // 更新所有带有data-i18n-placeholder属性的元素
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = this.t(key);
        });

        // 更新所有带有data-i18n-title属性的元素
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });

        // 更新动态内容
        this.updateDynamicContent();
    }

    // 更新动态内容
    updateDynamicContent() {
        // 更新分类选项
        this.updateCategoryOptions();
        
        // 更新商品状态选项
        this.updateConditionOptions();
        
        // 更新排序选项
        this.updateSortOptions();
    }

    // 更新分类选项
    updateCategoryOptions() {
        const categorySelects = document.querySelectorAll('#categoryFilter, #productCategory');
        categorySelects.forEach(select => {
            const firstOption = select.querySelector('option[value=""]');
            if (firstOption) {
                firstOption.textContent = this.t('filter.all_categories');
            }
        });
    }

    // 更新商品状态选项
    updateConditionOptions() {
        const conditionSelect = document.getElementById('productCondition');
        if (conditionSelect) {
            const options = conditionSelect.querySelectorAll('option');
            options.forEach(option => {
                const value = option.value;
                switch (value) {
                    case '全新':
                        option.textContent = this.t('publish.condition_new');
                        break;
                    case '几乎全新':
                        option.textContent = this.t('publish.condition_like_new');
                        break;
                    case '良好':
                        option.textContent = this.t('publish.condition_good');
                        break;
                    case '一般':
                        option.textContent = this.t('publish.condition_fair');
                        break;
                }
            });
        }
    }

    // 更新排序选项
    updateSortOptions() {
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            const options = sortSelect.querySelectorAll('option');
            options.forEach(option => {
                const value = option.value;
                switch (value) {
                    case 'newest':
                        option.textContent = this.t('filter.sort_newest');
                        break;
                    case 'oldest':
                        option.textContent = this.t('filter.sort_oldest');
                        break;
                    case 'price_low':
                        option.textContent = this.t('filter.sort_price_low');
                        break;
                    case 'price_high':
                        option.textContent = this.t('filter.sort_price_high');
                        break;
                    case 'views':
                        option.textContent = this.t('filter.sort_views');
                        break;
                    case 'favorites':
                        option.textContent = this.t('filter.sort_favorites');
                        break;
                }
            });
        }
    }

    // 格式化日期（考虑语言）
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) {
            return this.t('common.today');
        } else if (diffDays === 2) {
            return this.t('common.yesterday');
        } else if (diffDays <= 7) {
            return `${diffDays - 1}${this.t('common.days_ago')}`;
        } else {
            return this.currentLanguage === 'zh' 
                ? date.toLocaleDateString('zh-CN')
                : date.toLocaleDateString('en-US');
        }
    }

    // 获取当前语言
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // 检查是否为中文
    isZh() {
        return this.currentLanguage === 'zh';
    }

    // 检查是否为英文
    isEn() {
        return this.currentLanguage === 'en';
    }
}

// 创建国际化管理器实例
const i18n = new I18nManager();

// 全局翻译函数
window.t = (key, params) => i18n.t(key, params);

// 导出国际化管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = i18n;
}
