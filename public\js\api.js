// API调用模块

class API {
    constructor() {
        this.baseURL = '/api';
        this.token = storage.get('authToken');
    }

    // 设置认证令牌
    setToken(token) {
        this.token = token;
        if (token) {
            storage.set('authToken', token);
        } else {
            storage.remove('authToken');
        }
    }

    // 获取请求头
    getHeaders(includeAuth = true) {
        const headers = {
            'Content-Type': 'application/json'
        };

        if (includeAuth && this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        return headers;
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: this.getHeaders(options.auth !== false),
            ...options
        };

        try {
            showLoading();
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API请求错误:', error);
            throw error;
        } finally {
            hideLoading();
        }
    }

    // GET请求
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        return this.request(url, {
            method: 'GET'
        });
    }

    // POST请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE请求
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }

    // 文件上传请求
    async upload(endpoint, formData) {
        const url = `${this.baseURL}${endpoint}`;
        const headers = {};
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        try {
            showLoading();
            const response = await fetch(url, {
                method: 'POST',
                headers,
                body: formData
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('文件上传错误:', error);
            throw error;
        } finally {
            hideLoading();
        }
    }

    // 用户认证相关API
    auth = {
        // 用户注册
        register: async (userData) => {
            return this.post('/auth/register', userData);
        },

        // 用户登录
        login: async (credentials) => {
            return this.post('/auth/login', credentials);
        },

        // 获取当前用户信息
        getProfile: async () => {
            return this.get('/auth/me');
        },

        // 更新用户信息
        updateProfile: async (userData) => {
            return this.put('/auth/profile', userData);
        }
    };

    // 产品相关API
    products = {
        // 获取产品列表
        getList: async (params = {}) => {
            return this.get('/products', params);
        },

        // 获取产品详情
        getById: async (id) => {
            return this.get(`/products/${id}`);
        },

        // 创建产品
        create: async (formData) => {
            return this.upload('/products', formData);
        },

        // 更新产品
        update: async (id, productData) => {
            return this.put(`/products/${id}`, productData);
        },

        // 删除产品
        delete: async (id) => {
            return this.delete(`/products/${id}`);
        },

        // 获取用户的产品
        getMyProducts: async (params = {}) => {
            return this.get('/products/user/my-products', params);
        },

        // 获取分类列表
        getCategories: async () => {
            return this.get('/products/categories/list');
        }
    };

    // 收藏相关API
    favorites = {
        // 添加收藏
        add: async (productId) => {
            return this.post(`/favorites/${productId}`);
        },

        // 取消收藏
        remove: async (productId) => {
            return this.delete(`/favorites/${productId}`);
        },

        // 获取我的收藏列表
        getMyFavorites: async (params = {}) => {
            return this.get('/favorites/my-favorites', params);
        },

        // 检查收藏状态
        checkStatus: async (productId) => {
            return this.get(`/favorites/status/${productId}`);
        },

        // 批量检查收藏状态
        checkBatchStatus: async (productIds) => {
            return this.post('/favorites/status/batch', { productIds });
        },

        // 获取收藏数量
        getCount: async (productId) => {
            return this.get(`/favorites/count/${productId}`, {}, { auth: false });
        },

        // 获取收藏统计
        getStats: async () => {
            return this.get('/favorites/stats');
        }
    };

    // 留言相关API
    comments = {
        // 创建留言
        create: async (commentData) => {
            return this.post('/comments', commentData);
        },

        // 获取商品留言列表
        getByProduct: async (productId, params = {}) => {
            return this.get(`/comments/product/${productId}`, params);
        },

        // 获取留言详情
        getById: async (commentId) => {
            return this.get(`/comments/${commentId}`);
        },

        // 更新留言
        update: async (commentId, content) => {
            return this.put(`/comments/${commentId}`, { content });
        },

        // 删除留言
        delete: async (commentId) => {
            return this.delete(`/comments/${commentId}`);
        },

        // 获取我的留言
        getMyComments: async (params = {}) => {
            return this.get('/comments/user/my-comments', params);
        },

        // 获取留言统计
        getStats: async (productId) => {
            return this.get(`/comments/stats/${productId}`, {}, { auth: false });
        },

        // 搜索留言
        search: async (keyword, params = {}) => {
            return this.get('/comments/search', { keyword, ...params });
        }
    };

    // 健康检查
    health = {
        check: async () => {
            return this.get('/health', {}, { auth: false });
        }
    };
}

// 创建API实例
const api = new API();

// 响应拦截器 - 处理认证错误
const originalRequest = api.request.bind(api);
api.request = async function(endpoint, options = {}) {
    try {
        return await originalRequest(endpoint, options);
    } catch (error) {
        // 如果是认证错误，清除令牌并跳转到登录
        if (error.message.includes('访问被拒绝') || error.message.includes('无效的令牌')) {
            this.setToken(null);
            showToast('登录已过期，请重新登录', 'warning');
            // 可以在这里触发登录模态框
            return Promise.reject(error);
        }
        
        // 显示错误消息
        showToast(error.message || '请求失败', 'error');
        return Promise.reject(error);
    }
};

// 导出API实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = api;
}
