// 认证模块

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.init();
    }

    // 初始化认证状态
    async init() {
        const token = storage.get('authToken');
        if (token) {
            api.setToken(token);
            try {
                await this.loadCurrentUser();
            } catch (error) {
                console.error('加载用户信息失败:', error);
                this.logout();
            }
        }
        this.updateUI();
    }

    // 加载当前用户信息
    async loadCurrentUser() {
        try {
            const response = await api.auth.getProfile();
            this.currentUser = response.data.user;
            this.isAuthenticated = true;
            return this.currentUser;
        } catch (error) {
            this.currentUser = null;
            this.isAuthenticated = false;
            throw error;
        }
    }

    // 用户登录
    async login(credentials) {
        try {
            const response = await api.auth.login(credentials);
            const { user, token } = response.data;
            
            this.currentUser = user;
            this.isAuthenticated = true;
            api.setToken(token);
            
            this.updateUI();
            showToast('登录成功！', 'success');
            
            return user;
        } catch (error) {
            console.error('登录失败:', error);
            throw error;
        }
    }

    // 用户注册
    async register(userData) {
        try {
            const response = await api.auth.register(userData);
            const { user, token } = response.data;
            
            this.currentUser = user;
            this.isAuthenticated = true;
            api.setToken(token);
            
            this.updateUI();
            showToast('注册成功！', 'success');
            
            return user;
        } catch (error) {
            console.error('注册失败:', error);
            throw error;
        }
    }

    // 用户登出
    logout() {
        this.currentUser = null;
        this.isAuthenticated = false;
        api.setToken(null);
        
        this.updateUI();
        showToast('已退出登录', 'info');
        
        // 跳转到首页
        navigateTo('homePage');
    }

    // 更新UI显示
    updateUI() {
        const userMenu = document.getElementById('userMenu');
        const authButtons = document.getElementById('authButtons');
        const publishBtn = document.getElementById('publishBtn');
        const userName = document.getElementById('userName');

        if (this.isAuthenticated && this.currentUser) {
            // 显示用户菜单
            userMenu.classList.remove('hidden');
            authButtons.classList.add('hidden');
            publishBtn.classList.remove('hidden');
            
            // 设置用户名
            userName.textContent = this.currentUser.username;
        } else {
            // 显示登录/注册按钮
            userMenu.classList.add('hidden');
            authButtons.classList.remove('hidden');
            publishBtn.classList.add('hidden');
        }
    }

    // 检查是否已登录
    requireAuth() {
        if (!this.isAuthenticated) {
            showToast('请先登录', 'warning');
            this.showLoginModal();
            return false;
        }
        return true;
    }

    // 显示登录模态框
    showLoginModal() {
        const modal = document.getElementById('loginModal');
        modal.classList.add('active');
    }

    // 显示注册模态框
    showRegisterModal() {
        const modal = document.getElementById('registerModal');
        modal.classList.add('active');
    }

    // 隐藏所有模态框
    hideModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('active');
        });
    }
}

// 创建认证管理器实例
const authManager = new AuthManager();

// DOM加载完成后初始化事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 登录按钮
    document.getElementById('loginBtn').addEventListener('click', () => {
        authManager.showLoginModal();
    });

    // 注册按钮
    document.getElementById('registerBtn').addEventListener('click', () => {
        authManager.showRegisterModal();
    });

    // 退出登录按钮
    document.getElementById('logoutBtn').addEventListener('click', () => {
        authManager.logout();
    });

    // 个人资料链接
    document.getElementById('profileLink').addEventListener('click', (e) => {
        e.preventDefault();
        // TODO: 实现个人资料页面
        showToast('个人资料功能开发中...', 'info');
    });

    // 我的商品链接
    document.getElementById('myProductsLink').addEventListener('click', (e) => {
        e.preventDefault();
        if (authManager.requireAuth()) {
            navigateTo('myProductsPage');
            loadMyProducts();
        }
    });

    // 我的收藏链接
    document.getElementById('myFavoritesLink').addEventListener('click', (e) => {
        e.preventDefault();
        if (authManager.requireAuth()) {
            loadMyFavorites();
        }
    });

    // 发布商品按钮
    document.getElementById('publishBtn').addEventListener('click', () => {
        if (authManager.requireAuth()) {
            navigateTo('publishPage');
        }
    });

    // 模态框关闭按钮
    document.getElementById('closeLoginModal').addEventListener('click', () => {
        authManager.hideModals();
    });

    document.getElementById('closeRegisterModal').addEventListener('click', () => {
        authManager.hideModals();
    });

    // 点击模态框背景关闭
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                authManager.hideModals();
            }
        });
    });

    // 切换登录/注册模态框
    document.getElementById('switchToRegister').addEventListener('click', (e) => {
        e.preventDefault();
        authManager.hideModals();
        authManager.showRegisterModal();
    });

    document.getElementById('switchToLogin').addEventListener('click', (e) => {
        e.preventDefault();
        authManager.hideModals();
        authManager.showLoginModal();
    });

    // 登录表单提交
    document.getElementById('loginForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const credentials = {
            identifier: formData.get('identifier'),
            password: formData.get('password')
        };

        try {
            await authManager.login(credentials);
            authManager.hideModals();
            e.target.reset();
        } catch (error) {
            // 错误已在API模块中处理
        }
    });

    // 注册表单提交
    document.getElementById('registerForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const userData = {
            username: formData.get('username'),
            email: formData.get('email'),
            password: formData.get('password'),
            fullName: formData.get('fullName'),
            phone: formData.get('phone'),
            studentId: formData.get('studentId')
        };

        // 客户端验证
        if (!validateEmail(userData.email)) {
            showToast('请输入有效的邮箱地址', 'error');
            return;
        }

        if (userData.phone && !validatePhone(userData.phone)) {
            showToast('请输入有效的手机号码', 'error');
            return;
        }

        try {
            await authManager.register(userData);
            authManager.hideModals();
            e.target.reset();
        } catch (error) {
            // 错误已在API模块中处理
        }
    });
});

// 导出认证管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = authManager;
}
