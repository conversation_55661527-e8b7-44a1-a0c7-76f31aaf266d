const express = require('express');
const Comment = require('../models/Comment');
const { authenticateToken, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// 创建留言
router.post('/', authenticateToken, async (req, res) => {
    try {
        const { productId, content } = req.body;
        const userId = req.user.id;

        // 验证必填字段
        if (!productId || !content) {
            return res.status(400).json({
                success: false,
                message: '商品ID和留言内容不能为空'
            });
        }

        // 验证留言内容长度
        if (content.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: '留言内容不能为空'
            });
        }

        if (content.length > 500) {
            return res.status(400).json({
                success: false,
                message: '留言内容不能超过500字'
            });
        }

        // 创建留言
        const comment = await Comment.create({
            productId: parseInt(productId),
            userId,
            content: content.trim()
        });

        // 获取完整的留言信息（包含用户信息）
        const fullComment = await Comment.findById(comment.id);

        res.status(201).json({
            success: true,
            message: '留言发表成功',
            data: {
                comment: fullComment
            }
        });

    } catch (error) {
        console.error('创建留言错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取商品的留言列表
router.get('/product/:productId', optionalAuth, async (req, res) => {
    try {
        const productId = parseInt(req.params.productId);
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;

        if (isNaN(productId)) {
            return res.status(400).json({
                success: false,
                message: '无效的商品ID'
            });
        }

        const result = await Comment.getByProductId(productId, page, limit);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('获取留言列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取留言详情
router.get('/:id', async (req, res) => {
    try {
        const commentId = parseInt(req.params.id);

        if (isNaN(commentId)) {
            return res.status(400).json({
                success: false,
                message: '无效的留言ID'
            });
        }

        const comment = await Comment.findById(commentId);

        if (!comment) {
            return res.status(404).json({
                success: false,
                message: '留言不存在'
            });
        }

        res.json({
            success: true,
            data: {
                comment
            }
        });

    } catch (error) {
        console.error('获取留言详情错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 更新留言
router.put('/:id', authenticateToken, async (req, res) => {
    try {
        const commentId = parseInt(req.params.id);
        const { content } = req.body;
        const userId = req.user.id;

        if (isNaN(commentId)) {
            return res.status(400).json({
                success: false,
                message: '无效的留言ID'
            });
        }

        // 验证留言内容
        if (!content || content.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: '留言内容不能为空'
            });
        }

        if (content.length > 500) {
            return res.status(400).json({
                success: false,
                message: '留言内容不能超过500字'
            });
        }

        // 检查用户是否有权限修改此留言
        const canEdit = await Comment.canUserDelete(commentId, userId);
        if (!canEdit) {
            return res.status(403).json({
                success: false,
                message: '无权限修改此留言'
            });
        }

        // 更新留言
        const updatedComment = await Comment.update(commentId, content.trim());

        if (!updatedComment) {
            return res.status(404).json({
                success: false,
                message: '留言不存在'
            });
        }

        // 获取完整的留言信息
        const fullComment = await Comment.findById(commentId);

        res.json({
            success: true,
            message: '留言更新成功',
            data: {
                comment: fullComment
            }
        });

    } catch (error) {
        console.error('更新留言错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 删除留言
router.delete('/:id', authenticateToken, async (req, res) => {
    try {
        const commentId = parseInt(req.params.id);
        const userId = req.user.id;

        if (isNaN(commentId)) {
            return res.status(400).json({
                success: false,
                message: '无效的留言ID'
            });
        }

        // 检查用户是否有权限删除此留言
        const canDelete = await Comment.canUserDelete(commentId, userId);
        if (!canDelete) {
            return res.status(403).json({
                success: false,
                message: '无权限删除此留言'
            });
        }

        // 删除留言
        const deletedComment = await Comment.delete(commentId);

        if (!deletedComment) {
            return res.status(404).json({
                success: false,
                message: '留言不存在'
            });
        }

        res.json({
            success: true,
            message: '留言删除成功'
        });

    } catch (error) {
        console.error('删除留言错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取用户的留言列表
router.get('/user/my-comments', authenticateToken, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const userId = req.user.id;

        const comments = await Comment.getByUserId(userId, page, limit);

        res.json({
            success: true,
            data: {
                comments
            }
        });

    } catch (error) {
        console.error('获取用户留言错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取商品留言统计
router.get('/stats/:productId', async (req, res) => {
    try {
        const productId = parseInt(req.params.productId);

        if (isNaN(productId)) {
            return res.status(400).json({
                success: false,
                message: '无效的商品ID'
            });
        }

        const stats = await Comment.getCommentStats(productId);

        res.json({
            success: true,
            data: stats
        });

    } catch (error) {
        console.error('获取留言统计错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 搜索留言
router.get('/search', async (req, res) => {
    try {
        const { keyword } = req.query;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;

        if (!keyword || keyword.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: '搜索关键词不能为空'
            });
        }

        const result = await Comment.search(keyword.trim(), page, limit);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('搜索留言错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;
