// 产品管理模块

class ProductManager {
    constructor() {
        this.currentPage = 1;
        this.totalPages = 1;
        this.currentFilters = {};
        this.categories = [];
        this.selectedFiles = [];
        this.init();
    }

    // 初始化
    async init() {
        await this.loadCategories();
        await this.loadProducts();
        this.initEventListeners();
    }

    // 加载分类列表
    async loadCategories() {
        try {
            const response = await api.products.getCategories();
            this.categories = response.data.categories;
            this.renderCategoryOptions();
        } catch (error) {
            console.error('加载分类失败:', error);
        }
    }

    // 渲染分类选项
    renderCategoryOptions() {
        const categoryFilter = document.getElementById('categoryFilter');
        const productCategory = document.getElementById('productCategory');
        
        // 清空现有选项
        categoryFilter.innerHTML = '<option value="">所有分类</option>';
        productCategory.innerHTML = '<option value="">选择分类</option>';
        
        // 添加分类选项
        this.categories.forEach(category => {
            const option1 = new Option(category.name, category.id);
            const option2 = new Option(category.name, category.id);
            categoryFilter.appendChild(option1);
            productCategory.appendChild(option2);
        });
    }

    // 加载产品列表
    async loadProducts(page = 1, filters = {}) {
        try {
            const params = {
                page,
                limit: 12,
                ...filters
            };

            const response = await api.products.getList(params);
            const { products, total, totalPages } = response.data;

            this.currentPage = page;
            this.totalPages = totalPages;
            this.currentFilters = filters;

            if (page === 1) {
                this.renderProducts(products);
            } else {
                this.appendProducts(products);
            }

            this.updateLoadMoreButton();
        } catch (error) {
            console.error('加载产品失败:', error);
            showToast('加载产品失败', 'error');
        }
    }

    // 渲染产品列表
    renderProducts(products) {
        const grid = document.getElementById('productsGrid');
        grid.innerHTML = '';

        if (products.length === 0) {
            grid.innerHTML = `
                <div class="no-products">
                    <p>暂无商品</p>
                </div>
            `;
            return;
        }

        products.forEach(product => {
            const productCard = this.createProductCard(product);
            grid.appendChild(productCard);
        });
    }

    // 追加产品到列表
    appendProducts(products) {
        const grid = document.getElementById('productsGrid');
        
        products.forEach(product => {
            const productCard = this.createProductCard(product);
            grid.appendChild(productCard);
        });
    }

    // 创建产品卡片
    createProductCard(product) {
        const card = document.createElement('div');
        card.className = 'product-card';
        card.onclick = () => this.showProductDetail(product.id);

        const imageUrl = product.primary_image || '/images/placeholder.svg';
        const imageElement = product.primary_image
            ? `<img src="${imageUrl}" alt="${product.title}" class="product-image">`
            : `<div class="product-image">📷</div>`;

        // 收藏数量
        const favoriteCount = product.favorite_count || 0;

        card.innerHTML = `
            ${imageElement}
            <div class="product-info">
                <h3 class="product-title">${truncateText(product.title, 50)}</h3>
                <div class="product-price">${formatPrice(product.price)}</div>
                <div class="product-meta">
                    <span class="product-condition">${product.condition}</span>
                    <span>${i18n.formatDate(product.created_at)}</span>
                </div>
                <div class="product-stats">
                    <span class="favorites-count">
                        <span class="icon">❤️</span>
                        <span data-favorite-count="${product.id}">${favoriteCount}</span>
                    </span>
                    <span class="views-count">👁️ ${product.view_count || 0}</span>
                </div>
                <div class="product-seller">${t('product.seller')}: ${product.seller_name}</div>
            </div>
        `;

        // 如果用户已登录，添加收藏按钮
        if (authManager.isAuthenticated) {
            const favoriteBtn = favoriteManager.createFavoriteButton(product.id);
            card.appendChild(favoriteBtn);
        }

        return card;
    }

    // 显示产品详情
    async showProductDetail(productId) {
        try {
            const response = await api.products.getById(productId);
            const product = response.data.product;
            
            this.renderProductDetail(product);
            navigateTo('productDetailPage');
        } catch (error) {
            console.error('加载产品详情失败:', error);
            showToast('加载产品详情失败', 'error');
        }
    }

    // 渲染产品详情
    renderProductDetail(product) {
        const container = document.querySelector('.product-detail-container');

        const images = product.images || [];
        const mainImage = images.find(img => img.is_primary) || images[0];
        const thumbnails = images.filter(img => !img.is_primary);

        const imageSection = images.length > 0 ? `
            <div class="product-images">
                <img src="${mainImage.image_url}" alt="${product.title}" class="main-image" id="mainImage">
                ${thumbnails.length > 0 ? `
                    <div class="image-thumbnails">
                        ${images.map(img => `
                            <img src="${img.image_url}" alt="${product.title}"
                                 class="thumbnail ${img.is_primary ? 'active' : ''}"
                                 onclick="changeMainImage('${img.image_url}', this)">
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        ` : `
            <div class="product-images">
                <div class="main-image" style="display: flex; align-items: center; justify-content: center; background-color: var(--gray-100); color: var(--gray-400); font-size: 3rem;">📷</div>
            </div>
        `;

        const favoriteCount = product.favorite_count || 0;

        container.innerHTML = `
            <div class="product-detail-header">
                ${imageSection}
                <div class="product-details">
                    <h1 class="product-detail-title">${product.title}</h1>
                    <div class="product-detail-price">${formatPrice(product.price)}</div>

                    ${authManager.isAuthenticated ? `
                        <div class="product-actions-top">
                            <button id="favoriteBtn" class="btn btn-outline favorite-action" data-product-id="${product.id}">
                                <span class="favorite-icon">🤍</span>
                                <span data-i18n="product.add_to_favorites">${t('product.add_to_favorites')}</span>
                            </button>
                        </div>
                    ` : ''}

                    <div class="product-detail-meta">
                        <div class="meta-item">
                            <span class="meta-label" data-i18n="product.condition">${t('product.condition')}</span>
                            <span class="meta-value">${product.condition}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label" data-i18n="product.category">${t('product.category')}</span>
                            <span class="meta-value">${product.category_name || t('common.uncategorized')}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label" data-i18n="product.views">${t('product.views')}</span>
                            <span class="meta-value">${product.view_count || 0}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label" data-i18n="product.favorites_count">${t('product.favorites_count')}</span>
                            <span class="meta-value" data-favorite-count="${product.id}">${favoriteCount}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label" data-i18n="product.publish_time">${t('product.publish_time')}</span>
                            <span class="meta-value">${i18n.formatDate(product.created_at)}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="product-description">
                <h3 class="description-title" data-i18n="product.description">${t('product.description')}</h3>
                <div class="description-content">${product.description}</div>
            </div>

            <div class="seller-info">
                <h3 class="seller-title" data-i18n="product.seller_info">${t('product.seller_info')}</h3>
                <div class="seller-details">
                    <div class="meta-item">
                        <span class="meta-label" data-i18n="product.seller">${t('product.seller')}</span>
                        <span class="meta-value">${product.seller_name}</span>
                    </div>
                    ${product.seller_phone ? `
                        <div class="meta-item">
                            <span class="meta-label" data-i18n="product.contact_phone">${t('product.contact_phone')}</span>
                            <span class="meta-value">${product.seller_phone}</span>
                        </div>
                    ` : ''}
                </div>
            </div>

            <div class="product-actions">
                <button class="btn btn-outline" onclick="navigateTo('homePage')" data-i18n="product.back_to_list">${t('product.back_to_list')}</button>
                ${authManager.isAuthenticated && authManager.currentUser && authManager.currentUser.id === product.seller_id ? `
                    <button class="btn btn-primary" onclick="editProduct(${product.id})" data-i18n="product.edit_product">${t('product.edit_product')}</button>
                    <button class="btn btn-error" onclick="deleteProduct(${product.id})" data-i18n="product.delete_product">${t('product.delete_product')}</button>
                ` : ''}
            </div>
        `;

        // 如果用户已登录，设置收藏按钮状态
        if (authManager.isAuthenticated) {
            this.setupFavoriteButton(product.id);
        }

        // 添加留言区域
        commentManager.addCommentsSection(product.id, container);
    }

    // 更新加载更多按钮
    updateLoadMoreButton() {
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        
        if (this.currentPage < this.totalPages) {
            loadMoreBtn.classList.remove('hidden');
        } else {
            loadMoreBtn.classList.add('hidden');
        }
    }

    // 加载更多产品
    async loadMore() {
        if (this.currentPage < this.totalPages) {
            await this.loadProducts(this.currentPage + 1, this.currentFilters);
        }
    }

    // 应用筛选
    async applyFilters() {
        const filters = {
            category: document.getElementById('categoryFilter').value,
            search: document.getElementById('searchInput').value.trim(),
            minPrice: document.getElementById('minPrice').value,
            maxPrice: document.getElementById('maxPrice').value,
            sort: document.getElementById('sortSelect').value
        };

        // 移除空值
        Object.keys(filters).forEach(key => {
            if (!filters[key]) {
                delete filters[key];
            }
        });

        // 保存筛选状态到URL
        this.updateUrlParams(filters);

        await this.loadProducts(1, filters);
    }

    // 清除筛选
    async clearFilters() {
        document.getElementById('categoryFilter').value = '';
        document.getElementById('searchInput').value = '';
        document.getElementById('minPrice').value = '';
        document.getElementById('maxPrice').value = '';
        document.getElementById('sortSelect').value = 'newest';

        // 清除URL参数
        this.updateUrlParams({});

        await this.loadProducts(1, {});
    }

    // 初始化事件监听器
    initEventListeners() {
        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');

        const debouncedSearch = debounce(() => this.applyFilters(), 500);
        searchInput.addEventListener('input', debouncedSearch);
        searchBtn.addEventListener('click', () => this.applyFilters());

        // 筛选和排序
        document.getElementById('applyFilters').addEventListener('click', () => this.applyFilters());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearFilters());
        document.getElementById('sortSelect').addEventListener('change', () => this.applyFilters());

        // 加载更多按钮
        document.getElementById('loadMoreBtn').addEventListener('click', () => this.loadMore());

        // 发布商品相关事件
        this.initPublishEvents();

        // 从URL恢复筛选状态
        this.restoreFiltersFromUrl();
    }

    // 初始化发布商品事件
    initPublishEvents() {
        const publishForm = document.getElementById('publishForm');
        const imageInput = document.getElementById('productImages');
        const uploadPlaceholder = document.getElementById('uploadPlaceholder');
        const imagePreview = document.getElementById('imagePreview');
        const cancelBtn = document.getElementById('cancelPublish');

        // 图片上传区域点击事件
        uploadPlaceholder.addEventListener('click', () => {
            imageInput.click();
        });

        // 文件选择事件
        imageInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });

        // 取消发布
        cancelBtn.addEventListener('click', () => {
            this.resetPublishForm();
            navigateTo('homePage');
        });

        // 发布表单提交
        publishForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePublishSubmit(e);
        });
    }

    // 处理文件选择
    handleFileSelect(files) {
        const maxFiles = 5;
        const maxSize = 5 * 1024 * 1024; // 5MB

        // 检查文件数量
        if (this.selectedFiles.length + files.length > maxFiles) {
            showToast(`最多只能上传${maxFiles}张图片`, 'error');
            return;
        }

        Array.from(files).forEach(file => {
            // 检查文件大小
            if (file.size > maxSize) {
                showToast(`文件 ${file.name} 超过5MB限制`, 'error');
                return;
            }

            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                showToast(`文件 ${file.name} 不是图片格式`, 'error');
                return;
            }

            this.selectedFiles.push(file);
        });

        this.updateImagePreview();
    }

    // 更新图片预览
    updateImagePreview() {
        const uploadPlaceholder = document.getElementById('uploadPlaceholder');
        const imagePreview = document.getElementById('imagePreview');

        if (this.selectedFiles.length > 0) {
            uploadPlaceholder.classList.add('hidden');
            imagePreview.classList.remove('hidden');
            
            imagePreview.innerHTML = '';
            this.selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const previewItem = document.createElement('div');
                    previewItem.className = 'preview-item';
                    previewItem.innerHTML = `
                        <img src="${e.target.result}" alt="预览" class="preview-image">
                        <button type="button" class="remove-image" onclick="productManager.removeImage(${index})">×</button>
                    `;
                    imagePreview.appendChild(previewItem);
                };
                reader.readAsDataURL(file);
            });
        } else {
            uploadPlaceholder.classList.remove('hidden');
            imagePreview.classList.add('hidden');
        }
    }

    // 移除图片
    removeImage(index) {
        this.selectedFiles.splice(index, 1);
        this.updateImagePreview();
    }

    // 处理发布表单提交
    async handlePublishSubmit(e) {
        const formData = new FormData(e.target);
        
        // 添加图片文件
        this.selectedFiles.forEach(file => {
            formData.append('images', file);
        });

        try {
            const response = await api.products.create(formData);
            showToast('商品发布成功！', 'success');
            
            this.resetPublishForm();
            navigateTo('homePage');
            await this.loadProducts(); // 刷新产品列表
        } catch (error) {
            console.error('发布商品失败:', error);
        }
    }

    // 重置发布表单
    resetPublishForm() {
        document.getElementById('publishForm').reset();
        this.selectedFiles = [];
        this.updateImagePreview();
    }

    // 设置收藏按钮状态
    async setupFavoriteButton(productId) {
        const favoriteBtn = document.getElementById('favoriteBtn');
        if (!favoriteBtn) return;

        try {
            const isFavorited = await favoriteManager.getFavoriteStatus(productId);
            const icon = favoriteBtn.querySelector('.favorite-icon');
            const text = favoriteBtn.querySelector('span:last-child');

            if (isFavorited) {
                favoriteBtn.classList.add('favorited');
                icon.textContent = '❤️';
                text.textContent = t('product.remove_from_favorites');
            } else {
                favoriteBtn.classList.remove('favorited');
                icon.textContent = '🤍';
                text.textContent = t('product.add_to_favorites');
            }

            // 添加点击事件
            favoriteBtn.onclick = () => favoriteManager.toggleFavorite(productId, favoriteBtn);
        } catch (error) {
            console.error('设置收藏按钮失败:', error);
        }
    }

    // 更新URL参数
    updateUrlParams(filters) {
        const url = new URL(window.location);

        // 清除现有参数
        ['category', 'search', 'minPrice', 'maxPrice', 'sort'].forEach(key => {
            url.searchParams.delete(key);
        });

        // 添加新参数
        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                url.searchParams.set(key, filters[key]);
            }
        });

        // 更新URL但不刷新页面
        window.history.replaceState({}, '', url);
    }

    // 从URL恢复筛选状态
    restoreFiltersFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);

        const category = urlParams.get('category');
        const search = urlParams.get('search');
        const minPrice = urlParams.get('minPrice');
        const maxPrice = urlParams.get('maxPrice');
        const sort = urlParams.get('sort');

        if (category) document.getElementById('categoryFilter').value = category;
        if (search) document.getElementById('searchInput').value = search;
        if (minPrice) document.getElementById('minPrice').value = minPrice;
        if (maxPrice) document.getElementById('maxPrice').value = maxPrice;
        if (sort) document.getElementById('sortSelect').value = sort;

        // 如果有筛选参数，应用筛选
        if (category || search || minPrice || maxPrice || sort) {
            this.applyFilters();
        }
    }

    // 批量更新产品收藏状态
    async updateProductsFavoriteStatus(products) {
        if (!authManager.isAuthenticated || !products.length) {
            return;
        }

        const productIds = products.map(p => p.id);
        await favoriteManager.checkBatchFavoriteStatus(productIds);
    }
}

// 创建产品管理器实例
const productManager = new ProductManager();

// 全局函数
window.changeMainImage = function(imageUrl, thumbnail) {
    document.getElementById('mainImage').src = imageUrl;
    document.querySelectorAll('.thumbnail').forEach(thumb => {
        thumb.classList.remove('active');
    });
    thumbnail.classList.add('active');
};

window.editProduct = function(productId) {
    showToast('编辑功能开发中...', 'info');
};

window.deleteProduct = async function(productId) {
    if (confirm('确定要删除这个商品吗？')) {
        try {
            await api.products.delete(productId);
            showToast('商品删除成功', 'success');
            navigateTo('homePage');
            await productManager.loadProducts();
        } catch (error) {
            console.error('删除商品失败:', error);
        }
    }
};

// 加载我的商品
window.loadMyProducts = async function() {
    try {
        const response = await api.products.getMyProducts();
        const products = response.data.products;
        
        const grid = document.getElementById('myProductsGrid');
        grid.innerHTML = '';

        if (products.length === 0) {
            grid.innerHTML = `
                <div class="no-products">
                    <p>您还没有发布任何商品</p>
                    <button class="btn btn-primary" onclick="navigateTo('publishPage')">发布商品</button>
                </div>
            `;
            return;
        }

        products.forEach(product => {
            const productCard = productManager.createProductCard(product);
            grid.appendChild(productCard);
        });
    } catch (error) {
        console.error('加载我的商品失败:', error);
        showToast('加载我的商品失败', 'error');
    }
};

// 导出产品管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = productManager;
}
