// 主应用文件

class App {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    // 应用初始化
    async init() {
        if (this.isInitialized) return;

        try {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.start());
            } else {
                await this.start();
            }
        } catch (error) {
            console.error('应用初始化失败:', error);
            showToast('应用初始化失败', 'error');
        }
    }

    // 启动应用
    async start() {
        try {
            showLoading();

            // 初始化各个模块
            await this.initializeModules();

            // 设置全局事件监听器
            this.setupGlobalEventListeners();

            // 处理初始路由
            this.handleInitialRoute();

            // 标记为已初始化
            this.isInitialized = true;

            console.log('🎉 校园二手交易平台启动成功！');
        } catch (error) {
            console.error('应用启动失败:', error);
            showToast('应用启动失败，请刷新页面重试', 'error');
        } finally {
            hideLoading();
        }
    }

    // 初始化各个模块
    async initializeModules() {
        // 国际化模块已在i18n.js中自动初始化
        // 认证模块已在auth.js中自动初始化
        // 产品模块已在products.js中自动初始化
        // 收藏模块已在favorites.js中自动初始化
        // 留言模块已在comments.js中自动初始化

        // 检查服务器连接
        await this.checkServerHealth();
    }

    // 检查服务器健康状态
    async checkServerHealth() {
        try {
            await api.health.check();
            console.log('✅ 服务器连接正常');
        } catch (error) {
            console.warn('⚠️ 服务器连接异常:', error);
            showToast('服务器连接异常，部分功能可能不可用', 'warning');
        }
    }

    // 设置全局事件监听器
    setupGlobalEventListeners() {
        // 键盘快捷键
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

        // 窗口大小变化
        window.addEventListener('resize', debounce(this.handleWindowResize.bind(this), 250));

        // 页面可见性变化
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

        // 网络状态变化
        window.addEventListener('online', this.handleOnline.bind(this));
        window.addEventListener('offline', this.handleOffline.bind(this));

        // 阻止默认的拖拽行为
        document.addEventListener('dragover', (e) => e.preventDefault());
        document.addEventListener('drop', (e) => e.preventDefault());

        // 全局错误处理
        window.addEventListener('error', this.handleGlobalError.bind(this));
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));
    }

    // 处理键盘快捷键
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K: 聚焦搜索框
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            searchInput.focus();
            searchInput.select();
        }

        // Escape: 关闭模态框
        if (e.key === 'Escape') {
            authManager.hideModals();
        }

        // Ctrl/Cmd + Enter: 在发布页面提交表单
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const publishPage = document.getElementById('publishPage');
            if (publishPage.classList.contains('active')) {
                const submitBtn = publishPage.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.click();
                }
            }
        }
    }

    // 处理窗口大小变化
    handleWindowResize() {
        // 更新移动端检测
        document.body.classList.toggle('mobile', isMobile());
        
        // 在移动端自动关闭下拉菜单
        if (window.innerWidth < 768) {
            document.querySelectorAll('.dropdown-content').forEach(dropdown => {
                dropdown.style.opacity = '0';
                dropdown.style.visibility = 'hidden';
            });
        }
    }

    // 处理页面可见性变化
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('页面隐藏');
        } else {
            console.log('页面显示');
            // 页面重新显示时，可以刷新数据
            if (authManager.isAuthenticated) {
                // 可以在这里添加数据刷新逻辑
            }
        }
    }

    // 处理网络连接
    handleOnline() {
        showToast('网络连接已恢复', 'success');
        // 可以在这里重新加载数据
    }

    // 处理网络断开
    handleOffline() {
        showToast('网络连接已断开', 'warning');
    }

    // 处理全局错误
    handleGlobalError(event) {
        console.error('全局错误:', event.error);
        
        // 避免显示过多错误提示
        if (!this.lastErrorTime || Date.now() - this.lastErrorTime > 5000) {
            showToast('发生了一个错误，请刷新页面重试', 'error');
            this.lastErrorTime = Date.now();
        }
    }

    // 处理未捕获的Promise拒绝
    handleUnhandledRejection(event) {
        console.error('未处理的Promise拒绝:', event.reason);
        
        // 避免显示过多错误提示
        if (!this.lastRejectionTime || Date.now() - this.lastRejectionTime > 5000) {
            showToast('请求失败，请检查网络连接', 'error');
            this.lastRejectionTime = Date.now();
        }
    }

    // 处理初始路由
    handleInitialRoute() {
        // 检查URL参数
        const productId = getUrlParameter('product');
        if (productId) {
            productManager.showProductDetail(productId);
            return;
        }

        const page = getUrlParameter('page');
        if (page) {
            navigateTo(page);
            return;
        }

        // 默认显示首页
        navigateTo('homePage');
    }

    // 应用销毁
    destroy() {
        // 清理事件监听器
        document.removeEventListener('keydown', this.handleKeyboardShortcuts);
        window.removeEventListener('resize', this.handleWindowResize);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        window.removeEventListener('online', this.handleOnline);
        window.removeEventListener('offline', this.handleOffline);
        window.removeEventListener('error', this.handleGlobalError);
        window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);

        // 清理存储
        // storage.clear(); // 根据需要决定是否清理

        this.isInitialized = false;
        console.log('应用已销毁');
    }
}

// 创建应用实例
const app = new App();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    // 可以在这里保存用户数据或执行清理操作
    console.log('页面即将卸载');
});

// 开发环境下的调试工具
if (process?.env?.NODE_ENV === 'development' || window.location.hostname === 'localhost') {
    // 将主要对象暴露到全局作用域以便调试
    window.app = app;
    window.authManager = authManager;
    window.productManager = productManager;
    window.favoriteManager = favoriteManager;
    window.commentManager = commentManager;
    window.i18n = i18n;
    window.api = api;

    // 添加调试样式
    document.body.classList.add('debug-mode');

    console.log('🔧 调试模式已启用');
    console.log('可用的全局对象: app, authManager, productManager, favoriteManager, commentManager, i18n, api');
}

// 导出应用实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = app;
}
