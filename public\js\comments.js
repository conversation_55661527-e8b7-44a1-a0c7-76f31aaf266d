// 留言管理模块

class CommentManager {
    constructor() {
        this.currentProductId = null;
        this.comments = [];
        this.init();
    }

    // 初始化
    init() {
        this.setupEventListeners();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 使用事件委托处理留言相关事件
        document.addEventListener('click', (e) => {
            // 删除留言按钮
            if (e.target.classList.contains('delete-comment-btn')) {
                e.preventDefault();
                const commentId = parseInt(e.target.dataset.commentId);
                if (commentId) {
                    this.deleteComment(commentId);
                }
            }
        });

        // 留言表单提交
        document.addEventListener('submit', (e) => {
            if (e.target.id === 'commentForm') {
                e.preventDefault();
                this.submitComment(e.target);
            }
        });
    }

    // 为商品详情页添加留言区域
    addCommentsSection(productId, container) {
        this.currentProductId = productId;
        
        const commentsSection = document.createElement('div');
        commentsSection.className = 'comments-section';
        commentsSection.innerHTML = `
            <h3 class="comments-title" data-i18n="comments.title">${t('comments.title')}</h3>
            
            ${authManager.isAuthenticated ? `
                <form id="commentForm" class="comment-form">
                    <textarea 
                        id="commentInput" 
                        class="comment-input" 
                        data-i18n-placeholder="comments.comment_placeholder"
                        placeholder="${t('comments.comment_placeholder')}"
                        maxlength="500"
                        required
                    ></textarea>
                    <div class="comment-actions">
                        <span class="char-count">0/500</span>
                        <button type="submit" class="btn btn-primary" data-i18n="comments.submit_comment">
                            ${t('comments.submit_comment')}
                        </button>
                    </div>
                </form>
            ` : `
                <div class="login-prompt">
                    <p>${t('messages.login_required')}</p>
                    <button class="btn btn-primary" onclick="authManager.showLoginModal()">
                        ${t('nav.login')}
                    </button>
                </div>
            `}
            
            <div id="commentsList" class="comments-list">
                <!-- 留言列表将通过JavaScript动态加载 -->
            </div>
        `;

        container.appendChild(commentsSection);

        // 设置字符计数
        const commentInput = document.getElementById('commentInput');
        if (commentInput) {
            commentInput.addEventListener('input', (e) => {
                const charCount = e.target.value.length;
                const charCountElement = commentsSection.querySelector('.char-count');
                if (charCountElement) {
                    charCountElement.textContent = `${charCount}/500`;
                    charCountElement.style.color = charCount > 450 ? 'var(--error-color)' : 'var(--gray-500)';
                }
            });
        }

        // 加载留言列表
        this.loadComments(productId);
    }

    // 加载留言列表
    async loadComments(productId, page = 1) {
        try {
            const response = await api.comments.getByProduct(productId, { page, limit: 20 });
            const { comments, total, totalPages } = response.data;

            this.comments = page === 1 ? comments : [...this.comments, ...comments];
            this.renderComments();

            return { comments, total, totalPages };
        } catch (error) {
            console.error('加载留言失败:', error);
            showToast(t('messages.server_error'), 'error');
            return { comments: [], total: 0, totalPages: 0 };
        }
    }

    // 渲染留言列表
    renderComments() {
        const commentsList = document.getElementById('commentsList');
        if (!commentsList) return;

        if (this.comments.length === 0) {
            commentsList.innerHTML = `
                <div class="no-comments">
                    <p data-i18n="comments.no_comments">${t('comments.no_comments')}</p>
                </div>
            `;
            return;
        }

        commentsList.innerHTML = '';
        this.comments.forEach(comment => {
            const commentElement = this.createCommentElement(comment);
            commentsList.appendChild(commentElement);
        });
    }

    // 创建留言元素
    createCommentElement(comment) {
        const commentDiv = document.createElement('div');
        commentDiv.className = 'comment-item';
        commentDiv.dataset.commentId = comment.id;

        const canDelete = authManager.isAuthenticated && 
                         authManager.currentUser && 
                         authManager.currentUser.id === comment.user_id;

        commentDiv.innerHTML = `
            <div class="comment-header">
                <span class="comment-author">${comment.username}</span>
                <span class="comment-date">${i18n.formatDate(comment.created_at)}</span>
            </div>
            <div class="comment-content">${this.escapeHtml(comment.content)}</div>
            ${canDelete ? `
                <div class="comment-actions-bar">
                    <button class="delete-comment-btn" data-comment-id="${comment.id}" data-i18n="comments.delete_comment">
                        ${t('comments.delete_comment')}
                    </button>
                </div>
            ` : ''}
        `;

        return commentDiv;
    }

    // 提交留言
    async submitComment(form) {
        if (!authManager.requireAuth()) {
            return;
        }

        const formData = new FormData(form);
        const content = document.getElementById('commentInput').value.trim();

        if (!content) {
            showToast(t('messages.comment_required'), 'error');
            return;
        }

        if (content.length > 500) {
            showToast(t('messages.comment_too_long'), 'error');
            return;
        }

        try {
            const response = await api.comments.create({
                productId: this.currentProductId,
                content: content
            });

            showToast(t('messages.comment_added'), 'success');

            // 清空表单
            document.getElementById('commentInput').value = '';
            const charCountElement = document.querySelector('.char-count');
            if (charCountElement) {
                charCountElement.textContent = '0/500';
                charCountElement.style.color = 'var(--gray-500)';
            }

            // 将新留言添加到列表顶部
            this.comments.unshift(response.data.comment);
            this.renderComments();

        } catch (error) {
            console.error('提交留言失败:', error);
        }
    }

    // 删除留言
    async deleteComment(commentId) {
        if (!confirm(t('comments.confirm_delete'))) {
            return;
        }

        try {
            await api.comments.delete(commentId);
            showToast(t('messages.comment_deleted'), 'success');

            // 从列表中移除留言
            this.comments = this.comments.filter(comment => comment.id !== commentId);
            this.renderComments();

        } catch (error) {
            console.error('删除留言失败:', error);
        }
    }

    // 获取留言统计
    async getCommentStats(productId) {
        try {
            const response = await api.comments.getStats(productId);
            return response.data;
        } catch (error) {
            console.error('获取留言统计失败:', error);
            return null;
        }
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 清理当前留言数据
    clear() {
        this.currentProductId = null;
        this.comments = [];
    }

    // 搜索留言
    async searchComments(keyword, page = 1) {
        try {
            const response = await api.comments.search(keyword, { page, limit: 20 });
            return response.data;
        } catch (error) {
            console.error('搜索留言失败:', error);
            showToast(t('messages.server_error'), 'error');
            return { comments: [], total: 0, totalPages: 0 };
        }
    }

    // 获取用户的留言列表
    async getMyComments(page = 1) {
        if (!authManager.isAuthenticated) {
            return { comments: [], total: 0, totalPages: 0 };
        }

        try {
            const response = await api.comments.getMyComments({ page, limit: 20 });
            return response.data;
        } catch (error) {
            console.error('获取用户留言失败:', error);
            showToast(t('messages.server_error'), 'error');
            return { comments: [], total: 0, totalPages: 0 };
        }
    }
}

// 创建留言管理器实例
const commentManager = new CommentManager();

// 导出留言管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = commentManager;
}
