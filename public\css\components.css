/* 筛选栏样式 */
.filter-bar {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.filter-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.filter-select {
    min-width: 150px;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    font-size: var(--font-size-sm);
}

.price-filter {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.price-input {
    width: 120px;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
}

/* 商品网格样式 */
.products-container {
    margin-bottom: var(--spacing-2xl);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.product-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    cursor: pointer;
    border: 1px solid var(--gray-200);
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background-color: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    font-size: var(--font-size-lg);
}

.product-info {
    padding: var(--spacing-lg);
}

.product-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--error-color);
    margin-bottom: var(--spacing-sm);
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-bottom: var(--spacing-sm);
}

.product-condition {
    background-color: var(--success-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
}

.product-seller {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* 加载更多按钮 */
.load-more-container {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* 商品详情页样式 */
.product-detail-container {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-sm);
}

.product-detail-header {
    display: flex;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.product-images {
    flex: 1;
    max-width: 500px;
}

.main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
    background-color: var(--gray-100);
}

.image-thumbnails {
    display: flex;
    gap: var(--spacing-sm);
    overflow-x: auto;
}

.thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--radius-md);
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color var(--transition-fast);
}

.thumbnail.active {
    border-color: var(--primary-color);
}

.product-details {
    flex: 1;
}

.product-detail-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    line-height: 1.3;
}

.product-detail-price {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--error-color);
    margin-bottom: var(--spacing-lg);
}

.product-detail-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
}

.meta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.meta-label {
    font-weight: 500;
    color: var(--gray-600);
}

.meta-value {
    color: var(--gray-800);
}

.product-description {
    margin-bottom: var(--spacing-xl);
}

.description-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.description-content {
    color: var(--gray-700);
    line-height: 1.6;
    white-space: pre-wrap;
}

.seller-info {
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
}

.seller-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.seller-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

/* 发布商品页样式 */
.publish-container {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-sm);
    max-width: 600px;
    margin: 0 auto;
}

.publish-container h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.publish-form {
    margin-bottom: var(--spacing-xl);
}

.image-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: border-color var(--transition-fast);
}

.image-upload-area:hover {
    border-color: var(--primary-color);
}

.upload-placeholder {
    color: var(--gray-500);
}

.upload-icon {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
}

.upload-hint {
    font-size: var(--font-size-sm);
    color: var(--gray-400);
    margin-top: var(--spacing-sm);
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.preview-item {
    position: relative;
    border-radius: var(--radius-md);
    overflow: hidden;
}

.preview-image {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: 4px;
    right: 4px;
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-xl);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    width: 90%;
    max-width: 400px;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-800);
}

.close {
    font-size: var(--font-size-2xl);
    font-weight: bold;
    color: var(--gray-400);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.close:hover {
    color: var(--gray-600);
}

.auth-form {
    margin-bottom: var(--spacing-lg);
}

.auth-switch {
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.auth-switch a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* Toast消息样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--gray-800);
    color: white;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 3000;
    animation: toastSlideIn 0.3s ease-out;
}

.toast.success {
    background-color: var(--success-color);
}

.toast.error {
    background-color: var(--error-color);
}

.toast.warning {
    background-color: var(--warning-color);
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 加载指示器样式 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2500;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 语言选择器样式 */
.language-selector {
    position: relative;
    display: flex;
    align-items: center;
}

.language-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 0.5rem 0.75rem;
    background-color: transparent;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

.language-btn:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
}

.language-flag {
    font-size: var(--font-size-base);
}

.language-text {
    font-weight: 500;
}

.dropdown-arrow {
    font-size: var(--font-size-xs);
    transition: transform var(--transition-fast);
}

.language-dropdown.show .dropdown-arrow {
    transform: rotate(180deg);
}

.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--bg-primary);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 120px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    margin-top: var(--spacing-xs);
}

.language-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

.language-option:hover {
    background-color: var(--gray-50);
}

.language-option:first-child {
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.language-option:last-child {
    border-radius: 0 0 var(--radius-md) var(--radius-md);
}

.language-option .language-flag {
    font-size: var(--font-size-base);
}

.language-option .language-text {
    font-weight: 500;
}

/* 收藏按钮样式 */
.favorite-btn {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-lg);
    backdrop-filter: blur(4px);
}

.favorite-btn:hover {
    background-color: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

.favorite-btn.favorited {
    color: var(--error-color);
}

.favorite-btn:not(.favorited) {
    color: var(--gray-400);
}

.product-card {
    position: relative;
}

.favorites-count {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.favorites-count .icon {
    color: var(--error-color);
}

/* 留言区域样式 */
.comments-section {
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-2xl);
    border-top: 1px solid var(--gray-200);
}

.comments-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
}

.comment-form {
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
}

.comment-input {
    width: 100%;
    min-height: 80px;
    padding: var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    resize: vertical;
    font-family: inherit;
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-md);
}

.comment-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.comment-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.comment-item {
    background-color: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.comment-author {
    font-weight: 600;
    color: var(--gray-800);
}

.comment-date {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.comment-content {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-sm);
    white-space: pre-wrap;
}

.comment-actions-bar {
    display: flex;
    justify-content: flex-end;
}

.delete-comment-btn {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

.delete-comment-btn:hover {
    background-color: rgba(239, 68, 68, 0.1);
}

.no-comments {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
    padding: var(--spacing-xl);
}
