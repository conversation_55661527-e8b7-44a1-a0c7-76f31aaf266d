const { query } = require('../config/database');

// 数据库初始化脚本
async function initDatabase() {
  try {
    console.log('🚀 开始初始化数据库...');

    // 创建用户表
    await query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        student_id VARCHAR(20),
        avatar_url VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 用户表创建成功');

    // 创建产品分类表
    await query(`
      CREATE TABLE IF NOT EXISTS categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 分类表创建成功');

    // 创建产品表
    await query(`
      CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        description TEXT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        condition VARCHAR(20) DEFAULT '良好',
        category_id INTEGER REFERENCES categories(id),
        seller_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        status VARCHAR(20) DEFAULT '在售',
        view_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 产品表创建成功');

    // 创建产品图片表
    await query(`
      CREATE TABLE IF NOT EXISTS product_images (
        id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
        image_url VARCHAR(255) NOT NULL,
        is_primary BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 产品图片表创建成功');

    // 创建收藏表
    await query(`
      CREATE TABLE IF NOT EXISTS favorites (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, product_id)
      )
    `);
    console.log('✅ 收藏表创建成功');

    // 创建留言表
    await query(`
      CREATE TABLE IF NOT EXISTS comments (
        id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        content TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 留言表创建成功');

    // 插入默认分类
    await query(`
      INSERT INTO categories (name, description) VALUES
      ('电子产品', '手机、电脑、耳机等电子设备'),
      ('书籍教材', '教科书、参考书、小说等'),
      ('生活用品', '日用品、家居用品等'),
      ('服装配饰', '衣服、鞋子、包包等'),
      ('运动器材', '健身器材、球类、户外用品等'),
      ('其他', '其他类别商品')
      ON CONFLICT DO NOTHING
    `);
    console.log('✅ 默认分类插入成功');

    // 创建索引以提高查询性能
    await query(`CREATE INDEX IF NOT EXISTS idx_products_seller ON products(seller_id)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_products_status ON products(status)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_products_created ON products(created_at DESC)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_product_images_product ON product_images(product_id)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_favorites_user ON favorites(user_id)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_favorites_product ON favorites(product_id)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_comments_product ON comments(product_id)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_comments_user ON comments(user_id)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_comments_created ON comments(created_at DESC)`);
    console.log('✅ 数据库索引创建成功');

    console.log('🎉 数据库初始化完成！');
    process.exit(0);
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本，则执行初始化
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase };
