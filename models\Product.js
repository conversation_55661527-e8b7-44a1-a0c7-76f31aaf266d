const { query } = require('../config/database');

class Product {
  // 创建新产品
  static async create(productData) {
    const { title, description, price, condition, categoryId, sellerId } = productData;
    
    const result = await query(
      `INSERT INTO products (title, description, price, condition, category_id, seller_id)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [title, description, price, condition, categoryId, sellerId]
    );
    
    return result.rows[0];
  }

  // 获取所有产品（分页）
  static async findAll(page = 1, limit = 12, filters = {}) {
    const offset = (page - 1) * limit;
    let whereClause = "WHERE p.status = '在售'";
    const params = [];
    let paramCount = 1;

    // 添加筛选条件
    if (filters.category) {
      whereClause += ` AND p.category_id = $${paramCount}`;
      params.push(filters.category);
      paramCount++;
    }

    if (filters.search) {
      whereClause += ` AND (p.title ILIKE $${paramCount} OR p.description ILIKE $${paramCount})`;
      params.push(`%${filters.search}%`);
      paramCount++;
    }

    if (filters.minPrice) {
      whereClause += ` AND p.price >= $${paramCount}`;
      params.push(filters.minPrice);
      paramCount++;
    }

    if (filters.maxPrice) {
      whereClause += ` AND p.price <= $${paramCount}`;
      params.push(filters.maxPrice);
      paramCount++;
    }

    // 构建排序子句
    let orderClause = 'ORDER BY p.created_at DESC';
    if (filters.sort) {
      switch (filters.sort) {
        case 'newest':
          orderClause = 'ORDER BY p.created_at DESC';
          break;
        case 'oldest':
          orderClause = 'ORDER BY p.created_at ASC';
          break;
        case 'price_low':
          orderClause = 'ORDER BY p.price ASC';
          break;
        case 'price_high':
          orderClause = 'ORDER BY p.price DESC';
          break;
        case 'views':
          orderClause = 'ORDER BY p.view_count DESC';
          break;
        case 'favorites':
          orderClause = 'ORDER BY favorite_count DESC';
          break;
        default:
          orderClause = 'ORDER BY p.created_at DESC';
      }
    }

    // 添加分页参数
    params.push(limit, offset);

    const result = await query(
      `SELECT p.*, u.username as seller_name, c.name as category_name,
              pi.image_url as primary_image,
              COALESCE(f.favorite_count, 0) as favorite_count
       FROM products p
       LEFT JOIN users u ON p.seller_id = u.id
       LEFT JOIN categories c ON p.category_id = c.id
       LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = true
       LEFT JOIN (
         SELECT product_id, COUNT(*) as favorite_count
         FROM favorites
         GROUP BY product_id
       ) f ON p.id = f.product_id
       ${whereClause}
       ${orderClause}
       LIMIT $${paramCount} OFFSET $${paramCount + 1}`,
      params
    );

    // 获取总数
    const countResult = await query(
      `SELECT COUNT(*) FROM products p ${whereClause}`,
      params.slice(0, -2) // 移除limit和offset参数
    );

    return {
      products: result.rows,
      total: parseInt(countResult.rows[0].count),
      page,
      totalPages: Math.ceil(countResult.rows[0].count / limit)
    };
  }

  // 根据ID获取产品详情
  static async findById(id) {
    // 首先获取产品基本信息
    const result = await query(
      `SELECT p.*, u.username as seller_name, u.phone as seller_phone,
              c.name as category_name
       FROM products p
       LEFT JOIN users u ON p.seller_id = u.id
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.id = $1`,
      [id]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const product = result.rows[0];

    // 获取收藏数量
    const favoriteResult = await query(
      `SELECT COUNT(*) as favorite_count FROM favorites WHERE product_id = $1`,
      [id]
    );
    product.favorite_count = parseInt(favoriteResult.rows[0].favorite_count);

    // 获取产品图片
    const imagesResult = await query(
      `SELECT image_url, is_primary FROM product_images
       WHERE product_id = $1 ORDER BY is_primary DESC, id ASC`,
      [id]
    );

    product.images = imagesResult.rows;
    return product;
  }

  // 获取用户的产品
  static async findByUserId(userId, page = 1, limit = 12) {
    const offset = (page - 1) * limit;
    
    const result = await query(
      `SELECT p.*, c.name as category_name, pi.image_url as primary_image
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = true
       WHERE p.seller_id = $1
       ORDER BY p.created_at DESC
       LIMIT $2 OFFSET $3`,
      [userId, limit, offset]
    );

    return result.rows;
  }

  // 更新产品
  static async update(id, updateData) {
    const fields = [];
    const values = [];
    let paramCount = 1;

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = $${paramCount}`);
        values.push(updateData[key]);
        paramCount++;
      }
    });

    if (fields.length === 0) {
      throw new Error('没有要更新的字段');
    }

    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const result = await query(
      `UPDATE products SET ${fields.join(', ')} WHERE id = $${paramCount}
       RETURNING *`,
      values
    );

    return result.rows[0];
  }

  // 删除产品
  static async delete(id) {
    const result = await query(
      `DELETE FROM products WHERE id = $1 RETURNING *`,
      [id]
    );
    return result.rows[0];
  }

  // 增加浏览次数
  static async incrementViewCount(id) {
    await query(
      `UPDATE products SET view_count = view_count + 1 WHERE id = $1`,
      [id]
    );
  }

  // 添加产品图片
  static async addImage(productId, imageUrl, isPrimary = false) {
    const result = await query(
      `INSERT INTO product_images (product_id, image_url, is_primary)
       VALUES ($1, $2, $3) RETURNING *`,
      [productId, imageUrl, isPrimary]
    );
    return result.rows[0];
  }

  // 获取分类列表
  static async getCategories() {
    const result = await query(
      `SELECT * FROM categories ORDER BY id ASC`
    );
    return result.rows;
  }
}

module.exports = Product;
