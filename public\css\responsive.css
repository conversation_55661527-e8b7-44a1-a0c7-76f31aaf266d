/* 响应式设计 */

/* 大屏幕 (1200px+) */
@media (min-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .nav-container {
        padding: 0 var(--spacing-2xl);
    }
    
    .main-content {
        padding: var(--spacing-2xl);
    }
}

/* 中等屏幕 (768px - 1199px) */
@media (max-width: 1199px) and (min-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .nav-search {
        max-width: 300px;
        margin: 0 var(--spacing-lg);
    }
    
    .product-detail-header {
        gap: var(--spacing-xl);
    }
    
    .product-detail-meta {
        grid-template-columns: 1fr;
    }
    
    .seller-details {
        grid-template-columns: 1fr;
    }
}

/* 小屏幕 (576px - 767px) */
@media (max-width: 767px) and (min-width: 576px) {
    .nav-container {
        padding: 0 var(--spacing-md);
        flex-wrap: wrap;
        height: auto;
        min-height: 64px;
        padding-top: var(--spacing-md);
        padding-bottom: var(--spacing-md);
    }
    
    .nav-brand {
        order: 1;
        flex: 1;
    }
    
    .nav-menu {
        order: 2;
        flex-shrink: 0;
    }
    
    .nav-search {
        order: 3;
        width: 100%;
        max-width: none;
        margin: var(--spacing-md) 0 0 0;
    }
    
    .main-content {
        padding: var(--spacing-lg) var(--spacing-md);
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .filter-container {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }
    
    .filter-select,
    .price-input {
        width: 100%;
    }
    
    .price-filter {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .product-detail-header {
        flex-direction: column;
        gap: var(--spacing-lg);
    }
    
    .product-images {
        max-width: none;
    }
    
    .main-image {
        height: 300px;
    }
    
    .product-detail-title {
        font-size: var(--font-size-2xl);
    }
    
    .product-detail-price {
        font-size: var(--font-size-2xl);
    }
    
    .product-detail-meta {
        grid-template-columns: 1fr;
        padding: var(--spacing-md);
    }
    
    .seller-details {
        grid-template-columns: 1fr;
    }
    
    .publish-container {
        padding: var(--spacing-lg);
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        padding: var(--spacing-lg);
    }
}

/* 超小屏幕 (< 576px) */
@media (max-width: 575px) {
    .nav-container {
        padding: 0 var(--spacing-sm);
        flex-direction: column;
        height: auto;
        padding-top: var(--spacing-sm);
        padding-bottom: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
    
    .nav-brand h1 {
        font-size: var(--font-size-lg);
    }
    
    .nav-search {
        width: 100%;
        max-width: none;
        margin: 0;
    }
    
    .nav-menu {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .main-content {
        padding: var(--spacing-md) var(--spacing-sm);
    }
    
    .products-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .product-card {
        max-width: none;
    }
    
    .filter-bar {
        padding: var(--spacing-md);
    }
    
    .filter-container {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .filter-select,
    .price-input {
        width: 100%;
        min-width: auto;
    }
    
    .price-filter {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .product-detail-container {
        padding: var(--spacing-lg);
    }
    
    .product-detail-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .main-image {
        height: 250px;
    }
    
    .product-detail-title {
        font-size: var(--font-size-xl);
    }
    
    .product-detail-price {
        font-size: var(--font-size-xl);
    }
    
    .product-detail-meta {
        grid-template-columns: 1fr;
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
    
    .meta-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .seller-info {
        padding: var(--spacing-md);
    }
    
    .seller-details {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .publish-container {
        padding: var(--spacing-md);
    }
    
    .publish-container h2 {
        font-size: var(--font-size-xl);
    }
    
    .image-upload-area {
        padding: var(--spacing-lg);
    }
    
    .upload-icon {
        font-size: var(--font-size-2xl);
    }
    
    .image-preview {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .form-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .modal-content {
        width: 95%;
        padding: var(--spacing-md);
        margin: var(--spacing-sm);
    }
    
    .modal-header h3 {
        font-size: var(--font-size-lg);
    }
    
    .toast {
        top: 10px;
        right: 10px;
        left: 10px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    /* 用户菜单在小屏幕上的调整 */
    .user-menu {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);
    }
    
    .dropdown-content {
        position: fixed;
        top: auto;
        bottom: 60px;
        right: 10px;
        left: 10px;
        width: auto;
        min-width: auto;
    }
    
    /* 搜索框在小屏幕上的调整 */
    .search-box {
        width: 100%;
    }
    
    .search-box input {
        font-size: var(--font-size-base);
    }
    
    /* 按钮在小屏幕上的调整 */
    .btn {
        padding: 0.75rem 1rem;
        font-size: var(--font-size-base);
    }
    
    .auth-buttons {
        display: flex;
        gap: var(--spacing-xs);
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .auth-buttons .btn {
        flex: 1;
        min-width: 80px;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .filter-bar,
    .load-more-container,
    .form-actions,
    .modal,
    .toast,
    .loading {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        max-width: none;
    }
    
    .product-detail-container {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .product-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --gray-100: #f0f0f0;
        --gray-200: #e0e0e0;
        --gray-300: #c0c0c0;
        --gray-400: #a0a0a0;
        --gray-500: #808080;
        --gray-600: #606060;
        --gray-700: #404040;
        --gray-800: #202020;
        --gray-900: #000000;
    }
    
    .btn-outline {
        border-width: 2px;
    }
    
    .product-card {
        border-width: 2px;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        border-width: 2px;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .product-card:hover {
        transform: none;
    }
}
