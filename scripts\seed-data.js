const { query } = require('../config/database');
const bcrypt = require('bcryptjs');

// 创建测试数据
async function seedData() {
  try {
    console.log('🌱 开始创建测试数据...');

    // 创建测试用户
    const passwordHash = await bcrypt.hash('123456', 10);
    
    // 插入测试用户（如果不存在）
    await query(`
      INSERT INTO users (username, email, password_hash, full_name, phone, student_id)
      VALUES 
        ('testuser1', '<EMAIL>', $1, '张三', '13800138001', '2021001'),
        ('testuser2', '<EMAIL>', $1, '李四', '13800138002', '2021002'),
        ('testuser3', '<EMAIL>', $1, '王五', '13800138003', '2021003')
      ON CONFLICT (username) DO NOTHING
    `, [passwordHash]);
    console.log('✅ 测试用户创建成功');

    // 获取用户ID
    const usersResult = await query(`
      SELECT id, username FROM users WHERE username IN ('testuser1', 'testuser2', 'testuser3')
    `);
    const users = usersResult.rows;

    if (users.length === 0) {
      console.log('❌ 没有找到测试用户');
      return;
    }

    // 创建测试商品
    const products = [
      {
        title: 'iPhone 13 Pro 二手',
        description: '9成新iPhone 13 Pro，256GB，深空灰色。无磕碰，功能完好，配件齐全。因换新机出售。',
        price: 6800.00,
        condition: '几乎全新',
        category_id: 1, // 电子产品
        seller_id: users[0].id
      },
      {
        title: '高等数学教材',
        description: '同济大学版高等数学上下册，微积分与线性代数。书本干净，无涂画，适合数学专业学生。',
        price: 45.00,
        condition: '良好',
        category_id: 2, // 书籍教材
        seller_id: users[1].id
      },
      {
        title: '宿舍小冰箱',
        description: '美的小冰箱，容量50L，适合宿舍使用。制冷效果好，噪音小，因毕业出售。',
        price: 280.00,
        condition: '良好',
        category_id: 3, // 生活用品
        seller_id: users[0].id
      },
      {
        title: 'Nike运动鞋',
        description: 'Nike Air Max 270，42码，黑白配色。穿过几次，鞋底磨损很少，鞋盒保存完好。',
        price: 320.00,
        condition: '几乎全新',
        category_id: 4, // 服装配饰
        seller_id: users[2].id
      },
      {
        title: '羽毛球拍套装',
        description: 'YONEX羽毛球拍一对，带球包和羽毛球。适合初学者和业余爱好者，手感不错。',
        price: 150.00,
        condition: '良好',
        category_id: 5, // 运动器材
        seller_id: users[1].id
      },
      {
        title: '机械键盘',
        description: '樱桃轴机械键盘，青轴，87键无线版。手感极佳，适合编程和游戏，因升级设备出售。',
        price: 380.00,
        condition: '良好',
        category_id: 1, // 电子产品
        seller_id: users[2].id
      }
    ];

    for (const product of products) {
      await query(`
        INSERT INTO products (title, description, price, condition, category_id, seller_id)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [product.title, product.description, product.price, product.condition, product.category_id, product.seller_id]);
    }
    console.log('✅ 测试商品创建成功');

    // 创建一些测试收藏
    const productsResult = await query(`SELECT id FROM products LIMIT 3`);
    const productIds = productsResult.rows.map(p => p.id);

    if (productIds.length > 0 && users.length > 1) {
      // 用户1收藏前两个商品
      for (let i = 0; i < Math.min(2, productIds.length); i++) {
        await query(`
          INSERT INTO favorites (user_id, product_id)
          VALUES ($1, $2)
          ON CONFLICT (user_id, product_id) DO NOTHING
        `, [users[0].id, productIds[i]]);
      }

      // 用户2收藏第一个和第三个商品
      if (productIds.length > 2) {
        await query(`
          INSERT INTO favorites (user_id, product_id)
          VALUES ($1, $2), ($1, $3)
          ON CONFLICT (user_id, product_id) DO NOTHING
        `, [users[1].id, productIds[0], productIds[2]]);
      }
      console.log('✅ 测试收藏数据创建成功');
    }

    // 创建一些测试留言
    if (productIds.length > 0 && users.length > 1) {
      const comments = [
        { product_id: productIds[0], user_id: users[1].id, content: '这个商品还在吗？价格可以商量吗？' },
        { product_id: productIds[0], user_id: users[2].id, content: '看起来不错，什么时候可以看货？' },
        { product_id: productIds[1], user_id: users[0].id, content: '书的版本是最新的吗？' },
        { product_id: productIds[1], user_id: users[2].id, content: '可以打包优惠吗？我还需要其他教材。' }
      ];

      for (const comment of comments) {
        await query(`
          INSERT INTO comments (product_id, user_id, content)
          VALUES ($1, $2, $3)
        `, [comment.product_id, comment.user_id, comment.content]);
      }
      console.log('✅ 测试留言数据创建成功');
    }

    console.log('🎉 测试数据创建完成！');
    console.log('');
    console.log('测试账号信息：');
    console.log('用户名: testuser1, 密码: 123456');
    console.log('用户名: testuser2, 密码: 123456');
    console.log('用户名: testuser3, 密码: 123456');
    console.log('');
    console.log('现在可以使用这些账号登录测试新功能了！');

    process.exit(0);
  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本，则执行创建测试数据
if (require.main === module) {
  seedData();
}

module.exports = { seedData };
