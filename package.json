{"name": "campus-secondhand-platform", "version": "1.0.0", "description": "校园二手交易平台 - Campus Second-hand Trading Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-db.js", "seed-data": "node scripts/seed-data.js"}, "keywords": ["campus", "secondhand", "trading", "platform", "student"], "author": "Campus Platform Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}