# 校园二手交易平台

一个专为校园学生设计的二手商品交易平台，采用现代化的圆角扁平设计风格，提供简洁直观的用户体验。

## 🌟 功能特性

### 核心功能
- 🔐 **用户认证系统** - 注册、登录、个人资料管理
- 📱 **商品管理** - 发布、浏览、搜索、编辑、删除商品
- 🖼️ **图片上传** - 支持多图片上传，自动生成缩略图
- 🔍 **智能搜索** - 支持关键词搜索和分类筛选
- 📊 **分类管理** - 预设商品分类，便于商品组织
- 🌍 **国际化支持** - 中英文双语切换，本地化用户体验
- ❤️ **收藏功能** - 商品收藏/取消收藏，我的收藏列表
- 💬 **留言系统** - 商品留言板，用户互动交流
- 🔄 **智能排序** - 多维度商品排序（时间、价格、热度等）

### 设计特色
- 🎨 **圆角扁平设计** - 现代化的UI设计风格
- 📱 **响应式布局** - 完美适配各种屏幕尺寸
- ⚡ **快速加载** - 优化的前端性能
- 🎯 **用户友好** - 简洁直观的操作界面

## 🛠️ 技术栈

### 前端
- **HTML5** - 语义化标记
- **CSS3** - 现代化样式，支持响应式设计
- **JavaScript (ES6+)** - 原生JavaScript，无框架依赖

### 后端
- **Node.js** - 服务器运行环境
- **Express.js** - Web应用框架
- **PostgreSQL** - 关系型数据库
- **JWT** - 用户认证
- **Multer** - 文件上传处理

### 安全特性
- 密码加密存储 (bcryptjs)
- JWT令牌认证
- 文件上传安全检查
- SQL注入防护
- 请求频率限制

## 🚀 快速开始

### 环境要求
- Node.js 16.0+
- PostgreSQL 12.0+
- npm 或 yarn

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd Campus-SH-Platform2.0
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入你的数据库配置
   ```

4. **初始化数据库**
   ```bash
   npm run init-db
   ```

5. **启动应用**
   ```bash
   # 开发模式
   npm run dev
   
   # 生产模式
   npm start
   ```

6. **访问应用**
   打开浏览器访问 `http://localhost:3000`

## 📁 项目结构

```
Campus-SH-Platform2.0/
├── public/                 # 前端静态文件
│   ├── css/               # 样式文件
│   │   ├── style.css      # 主样式
│   │   ├── components.css # 组件样式
│   │   └── responsive.css # 响应式样式
│   ├── js/                # JavaScript文件
│   │   ├── utils.js       # 工具函数
│   │   ├── api.js         # API调用
│   │   ├── auth.js        # 认证管理
│   │   ├── products.js    # 商品管理
│   │   └── app.js         # 主应用
│   ├── images/            # 图片资源
│   └── index.html         # 主页面
├── config/                # 配置文件
│   └── database.js        # 数据库配置
├── models/                # 数据模型
│   ├── User.js           # 用户模型
│   └── Product.js        # 商品模型
├── routes/                # 路由文件
│   ├── auth.js           # 认证路由
│   └── products.js       # 商品路由
├── middleware/            # 中间件
│   ├── auth.js           # 认证中间件
│   └── upload.js         # 文件上传中间件
├── scripts/               # 脚本文件
│   └── init-db.js        # 数据库初始化
├── uploads/               # 上传文件目录
├── server.js             # 服务器入口
├── package.json          # 项目配置
└── README.md            # 项目文档
```

## 🎯 使用指南

### 用户注册和登录
1. 点击导航栏的"注册"按钮
2. 填写用户信息（用户名、邮箱、密码、真实姓名等）
3. 注册成功后自动登录

### 发布商品
1. 登录后点击"发布商品"按钮
2. 填写商品信息（标题、描述、价格、状态等）
3. 上传商品图片（最多5张）
4. 点击"发布商品"完成发布

### 浏览和搜索商品
1. 在首页浏览所有商品
2. 使用搜索框输入关键词搜索
3. 使用分类筛选器按分类浏览
4. 设置价格范围筛选
5. 点击商品卡片查看详情

### 管理我的商品
1. 点击用户菜单中的"我的商品"
2. 查看已发布的商品列表
3. 编辑或删除商品

## 🔧 开发指南

### 数据库设计
- `users` - 用户表
- `categories` - 分类表
- `products` - 商品表
- `product_images` - 商品图片表
- `favorites` - 收藏表

### API接口

#### 用户认证
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取用户信息
- `PUT /api/auth/profile` - 更新用户信息

#### 商品管理
- `GET /api/products` - 获取商品列表（支持筛选和排序）
- `POST /api/products` - 创建商品
- `GET /api/products/:id` - 获取商品详情
- `PUT /api/products/:id` - 更新商品
- `DELETE /api/products/:id` - 删除商品
- `GET /api/products/user/my-products` - 获取我的商品
- `GET /api/products/categories/list` - 获取分类列表

#### 收藏功能
- `POST /api/favorites/:productId` - 添加收藏
- `DELETE /api/favorites/:productId` - 取消收藏
- `GET /api/favorites/my-favorites` - 获取我的收藏
- `GET /api/favorites/status/:productId` - 检查收藏状态
- `GET /api/favorites/count/:productId` - 获取收藏数量

#### 留言功能
- `POST /api/comments` - 创建留言
- `GET /api/comments/product/:productId` - 获取商品留言
- `DELETE /api/comments/:id` - 删除留言
- `GET /api/comments/user/my-comments` - 获取我的留言

### 前端架构
- 模块化设计，每个功能独立封装
- 事件驱动的交互模式
- 响应式设计，支持多设备
- 原生JavaScript，无框架依赖

## 🔒 安全考虑

- 密码使用bcrypt加密存储
- JWT令牌用于用户认证
- 文件上传类型和大小限制
- SQL查询使用参数化防止注入
- 请求频率限制防止滥用
- CORS配置控制跨域访问

## 🚀 部署指南

### 生产环境部署
1. 设置环境变量 `NODE_ENV=production`
2. 配置生产数据库
3. 使用PM2或类似工具管理进程
4. 配置Nginx反向代理
5. 启用HTTPS

### Docker部署
```dockerfile
# Dockerfile示例
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📝 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**校园二手交易平台** - 让校园交易更简单、更安全、更便捷！
