<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校园二手交易平台</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/components.css">
    <link rel="stylesheet" href="/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1 data-i18n="nav.brand">🛒 校园二手</h1>
            </div>

            <div class="nav-search">
                <div class="search-box">
                    <input type="text" id="searchInput" data-i18n-placeholder="nav.search_placeholder" placeholder="搜索商品...">
                    <button id="searchBtn" class="search-btn">🔍</button>
                </div>
            </div>

            <div class="nav-menu">
                <div id="userMenu" class="user-menu hidden">
                    <span id="userName" class="user-name"></span>
                    <div class="dropdown">
                        <button class="dropdown-btn">▼</button>
                        <div class="dropdown-content">
                            <a href="#" id="myProductsLink" data-i18n="nav.my_products">我的商品</a>
                            <a href="#" id="myFavoritesLink" data-i18n="nav.my_favorites">我的收藏</a>
                            <a href="#" id="profileLink" data-i18n="nav.profile">个人资料</a>
                            <a href="#" id="logoutBtn" data-i18n="nav.logout">退出登录</a>
                        </div>
                    </div>
                </div>

                <div id="authButtons" class="auth-buttons">
                    <button id="loginBtn" class="btn btn-outline" data-i18n="nav.login">登录</button>
                    <button id="registerBtn" class="btn btn-primary" data-i18n="nav.register">注册</button>
                </div>

                <button id="publishBtn" class="btn btn-success hidden" data-i18n="nav.publish">发布商品</button>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 首页内容 -->
        <div id="homePage" class="page active">
            <!-- 筛选栏 -->
            <div class="filter-bar">
                <div class="filter-container">
                    <select id="categoryFilter" class="filter-select">
                        <option value="" data-i18n="filter.all_categories">所有分类</option>
                    </select>

                    <div class="price-filter">
                        <input type="number" id="minPrice" data-i18n-placeholder="filter.min_price" placeholder="最低价格" class="price-input">
                        <span>-</span>
                        <input type="number" id="maxPrice" data-i18n-placeholder="filter.max_price" placeholder="最高价格" class="price-input">
                    </div>

                    <select id="sortSelect" class="filter-select">
                        <option value="newest" data-i18n="filter.sort_newest">最新发布</option>
                        <option value="oldest" data-i18n="filter.sort_oldest">最早发布</option>
                        <option value="price_low" data-i18n="filter.sort_price_low">价格从低到高</option>
                        <option value="price_high" data-i18n="filter.sort_price_high">价格从高到低</option>
                        <option value="views" data-i18n="filter.sort_views">浏览量最多</option>
                        <option value="favorites" data-i18n="filter.sort_favorites">收藏最多</option>
                    </select>

                    <button id="applyFilters" class="btn btn-primary" data-i18n="filter.apply_filters">筛选</button>
                    <button id="clearFilters" class="btn btn-outline" data-i18n="filter.clear_filters">清除</button>
                </div>
            </div>

            <!-- 商品网格 -->
            <div class="products-container">
                <div id="productsGrid" class="products-grid">
                    <!-- 商品卡片将通过JavaScript动态加载 -->
                </div>
                
                <!-- 加载更多按钮 -->
                <div class="load-more-container">
                    <button id="loadMoreBtn" class="btn btn-outline hidden">加载更多</button>
                </div>
            </div>
        </div>

        <!-- 商品详情页 -->
        <div id="productDetailPage" class="page">
            <div class="product-detail-container">
                <!-- 商品详情内容将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 发布商品页 -->
        <div id="publishPage" class="page">
            <div class="publish-container">
                <h2 data-i18n="publish.title">发布商品</h2>
                <form id="publishForm" class="publish-form">
                    <div class="form-group">
                        <label for="productTitle" data-i18n="publish.product_title">商品标题 *</label>
                        <input type="text" id="productTitle" name="title" required maxlength="100">
                    </div>

                    <div class="form-group">
                        <label for="productCategory" data-i18n="publish.product_category">商品分类</label>
                        <select id="productCategory" name="categoryId">
                            <option value="" data-i18n="publish.select_category">选择分类</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="productPrice" data-i18n="publish.price">价格 (元) *</label>
                        <input type="number" id="productPrice" name="price" required min="0" step="0.01">
                    </div>

                    <div class="form-group">
                        <label for="productCondition" data-i18n="publish.condition">商品状态</label>
                        <select id="productCondition" name="condition">
                            <option value="全新" data-i18n="publish.condition_new">全新</option>
                            <option value="几乎全新" data-i18n="publish.condition_like_new">几乎全新</option>
                            <option value="良好" selected data-i18n="publish.condition_good">良好</option>
                            <option value="一般" data-i18n="publish.condition_fair">一般</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="productDescription" data-i18n="publish.description">商品描述 *</label>
                        <textarea id="productDescription" name="description" required rows="5" maxlength="1000"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="productImages" data-i18n="publish.images">商品图片 (最多5张)</label>
                        <div class="image-upload-area">
                            <input type="file" id="productImages" name="images" multiple accept="image/*" class="hidden">
                            <div class="upload-placeholder" id="uploadPlaceholder">
                                <div class="upload-icon">📷</div>
                                <p data-i18n="publish.upload_hint">点击上传图片</p>
                                <p class="upload-hint" data-i18n="publish.upload_support">支持JPG、PNG格式，单张图片不超过5MB</p>
                            </div>
                            <div id="imagePreview" class="image-preview hidden"></div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="cancelPublish" class="btn btn-outline" data-i18n="publish.cancel">取消</button>
                        <button type="submit" class="btn btn-primary" data-i18n="publish.publish_btn">发布商品</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 我的商品页 -->
        <div id="myProductsPage" class="page">
            <div class="my-products-container">
                <h2 data-i18n="nav.my_products">我的商品</h2>
                <div id="myProductsGrid" class="products-grid">
                    <!-- 我的商品将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>

        <!-- 我的收藏页 -->
        <div id="myFavoritesPage" class="page">
            <div class="my-favorites-container">
                <h2 data-i18n="favorites.title">我的收藏</h2>
                <div id="myFavoritesGrid" class="products-grid">
                    <!-- 我的收藏将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </main>

    <!-- 登录模态框 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-i18n="auth.login_title">登录</h3>
                <span class="close" id="closeLoginModal">&times;</span>
            </div>
            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="loginIdentifier" data-i18n="auth.username_or_email">用户名或邮箱</label>
                    <input type="text" id="loginIdentifier" name="identifier" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword" data-i18n="auth.password">密码</label>
                    <input type="password" id="loginPassword" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary btn-full" data-i18n="auth.login_btn">登录</button>
            </form>
            <p class="auth-switch">
                <span data-i18n="auth.no_account">还没有账号？</span><a href="#" id="switchToRegister" data-i18n="auth.register_now">立即注册</a>
            </p>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div id="registerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-i18n="auth.register_title">注册</h3>
                <span class="close" id="closeRegisterModal">&times;</span>
            </div>
            <form id="registerForm" class="auth-form">
                <div class="form-group">
                    <label for="registerUsername" data-i18n="auth.username">用户名</label>
                    <input type="text" id="registerUsername" name="username" required minlength="3" maxlength="20">
                </div>
                <div class="form-group">
                    <label for="registerEmail" data-i18n="auth.email">邮箱</label>
                    <input type="email" id="registerEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword" data-i18n="auth.password">密码</label>
                    <input type="password" id="registerPassword" name="password" required minlength="6">
                </div>
                <div class="form-group">
                    <label for="registerFullName" data-i18n="auth.full_name">真实姓名</label>
                    <input type="text" id="registerFullName" name="fullName" required>
                </div>
                <div class="form-group">
                    <label for="registerPhone" data-i18n="auth.phone">手机号码</label>
                    <input type="tel" id="registerPhone" name="phone">
                </div>
                <div class="form-group">
                    <label for="registerStudentId" data-i18n="auth.student_id">学号</label>
                    <input type="text" id="registerStudentId" name="studentId">
                </div>
                <button type="submit" class="btn btn-primary btn-full" data-i18n="auth.register_btn">注册</button>
            </form>
            <p class="auth-switch">
                <span data-i18n="auth.has_account">已有账号？</span><a href="#" id="switchToLogin" data-i18n="auth.login_now">立即登录</a>
            </p>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast hidden">
        <span id="toastMessage"></span>
    </div>

    <!-- 加载指示器 -->
    <div id="loading" class="loading hidden">
        <div class="spinner"></div>
    </div>

    <!-- JavaScript文件 -->
    <script src="/js/utils.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/i18n.js"></script>
    <script src="/js/favorites.js"></script>
    <script src="/js/comments.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/products.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>
