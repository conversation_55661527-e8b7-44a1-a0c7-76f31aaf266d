// 收藏管理模块

class FavoriteManager {
    constructor() {
        this.favoriteStatus = new Map(); // 缓存收藏状态
        this.init();
    }

    // 初始化
    init() {
        this.setupEventListeners();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 使用事件委托处理收藏按钮点击
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('favorite-btn') || e.target.closest('.favorite-btn')) {
                e.preventDefault();
                e.stopPropagation();
                
                const btn = e.target.classList.contains('favorite-btn') ? e.target : e.target.closest('.favorite-btn');
                const productId = parseInt(btn.dataset.productId);
                
                if (productId) {
                    this.toggleFavorite(productId, btn);
                }
            }
        });
    }

    // 切换收藏状态
    async toggleFavorite(productId, btn) {
        if (!authManager.requireAuth()) {
            return;
        }

        try {
            const isFavorited = btn.classList.contains('favorited');
            
            // 立即更新UI（乐观更新）
            this.updateFavoriteButton(btn, !isFavorited);
            
            let response;
            if (isFavorited) {
                response = await api.favorites.remove(productId);
                showToast(t('messages.favorite_removed'), 'success');
            } else {
                response = await api.favorites.add(productId);
                showToast(t('messages.favorite_added'), 'success');
            }

            // 更新收藏数量显示
            if (response.data.favoriteCount !== undefined) {
                this.updateFavoriteCount(productId, response.data.favoriteCount);
            }

            // 更新缓存
            this.favoriteStatus.set(productId, !isFavorited);

        } catch (error) {
            console.error('切换收藏状态失败:', error);
            // 恢复UI状态
            this.updateFavoriteButton(btn, btn.classList.contains('favorited'));
            showToast(t('messages.server_error'), 'error');
        }
    }

    // 更新收藏按钮状态
    updateFavoriteButton(btn, isFavorited) {
        if (isFavorited) {
            btn.classList.add('favorited');
            btn.innerHTML = '❤️';
            btn.title = t('product.remove_from_favorites');
        } else {
            btn.classList.remove('favorited');
            btn.innerHTML = '🤍';
            btn.title = t('product.add_to_favorites');
        }
    }

    // 更新收藏数量显示
    updateFavoriteCount(productId, count) {
        const countElements = document.querySelectorAll(`[data-favorite-count="${productId}"]`);
        countElements.forEach(element => {
            element.textContent = count;
        });
    }

    // 创建收藏按钮
    createFavoriteButton(productId, isFavorited = false) {
        const btn = document.createElement('button');
        btn.className = 'favorite-btn';
        btn.dataset.productId = productId;
        
        this.updateFavoriteButton(btn, isFavorited);
        
        return btn;
    }

    // 批量检查收藏状态
    async checkBatchFavoriteStatus(productIds) {
        if (!authManager.isAuthenticated || !productIds.length) {
            return {};
        }

        try {
            const response = await api.favorites.checkBatchStatus(productIds);
            const favoriteStatus = response.data;

            // 更新缓存
            Object.keys(favoriteStatus).forEach(productId => {
                this.favoriteStatus.set(parseInt(productId), favoriteStatus[productId]);
            });

            // 更新UI
            this.updateFavoriteButtonsFromStatus(favoriteStatus);

            return favoriteStatus;
        } catch (error) {
            console.error('批量检查收藏状态失败:', error);
            return {};
        }
    }

    // 根据状态更新收藏按钮
    updateFavoriteButtonsFromStatus(favoriteStatus) {
        Object.keys(favoriteStatus).forEach(productId => {
            const buttons = document.querySelectorAll(`[data-product-id="${productId}"].favorite-btn`);
            buttons.forEach(btn => {
                this.updateFavoriteButton(btn, favoriteStatus[productId]);
            });
        });
    }

    // 获取收藏状态（从缓存或API）
    async getFavoriteStatus(productId) {
        if (this.favoriteStatus.has(productId)) {
            return this.favoriteStatus.get(productId);
        }

        if (!authManager.isAuthenticated) {
            return false;
        }

        try {
            const response = await api.favorites.checkStatus(productId);
            const isFavorited = response.data.isFavorited;
            this.favoriteStatus.set(productId, isFavorited);
            return isFavorited;
        } catch (error) {
            console.error('获取收藏状态失败:', error);
            return false;
        }
    }

    // 加载我的收藏列表
    async loadMyFavorites(page = 1) {
        if (!authManager.requireAuth()) {
            return;
        }

        try {
            const response = await api.favorites.getMyFavorites({ page, limit: 12 });
            const { favorites, total, totalPages } = response.data;

            this.renderFavoritesList(favorites);
            
            return { favorites, total, totalPages };
        } catch (error) {
            console.error('加载收藏列表失败:', error);
            showToast(t('messages.server_error'), 'error');
            return { favorites: [], total: 0, totalPages: 0 };
        }
    }

    // 渲染收藏列表
    renderFavoritesList(favorites) {
        const grid = document.getElementById('myFavoritesGrid');
        if (!grid) return;

        grid.innerHTML = '';

        if (favorites.length === 0) {
            grid.innerHTML = `
                <div class="no-favorites">
                    <p data-i18n="favorites.no_favorites">${t('favorites.no_favorites')}</p>
                    <button class="btn btn-primary" onclick="navigateTo('homePage')" data-i18n="favorites.browse_products">${t('favorites.browse_products')}</button>
                </div>
            `;
            return;
        }

        favorites.forEach(product => {
            const productCard = productManager.createProductCard(product);
            
            // 添加收藏按钮
            const favoriteBtn = this.createFavoriteButton(product.id, true);
            productCard.appendChild(favoriteBtn);
            
            grid.appendChild(productCard);
        });
    }

    // 清除收藏状态缓存
    clearCache() {
        this.favoriteStatus.clear();
    }

    // 获取收藏统计
    async getFavoriteStats() {
        if (!authManager.isAuthenticated) {
            return null;
        }

        try {
            const response = await api.favorites.getStats();
            return response.data;
        } catch (error) {
            console.error('获取收藏统计失败:', error);
            return null;
        }
    }
}

// 创建收藏管理器实例
const favoriteManager = new FavoriteManager();

// 全局函数
window.loadMyFavorites = async function() {
    navigateTo('myFavoritesPage');
    await favoriteManager.loadMyFavorites();
};

// 导出收藏管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = favoriteManager;
}
