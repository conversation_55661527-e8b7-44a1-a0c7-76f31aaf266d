const express = require('express');
const Favorite = require('../models/Favorite');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 添加收藏
router.post('/:productId', authenticateToken, async (req, res) => {
    try {
        const productId = parseInt(req.params.productId);
        const userId = req.user.id;

        if (isNaN(productId)) {
            return res.status(400).json({
                success: false,
                message: '无效的商品ID'
            });
        }

        // 检查是否已经收藏
        const isAlreadyFavorited = await Favorite.isFavorited(userId, productId);
        if (isAlreadyFavorited) {
            return res.status(400).json({
                success: false,
                message: '商品已在收藏列表中'
            });
        }

        // 添加收藏
        const favorite = await Favorite.add(userId, productId);
        
        // 获取更新后的收藏数量
        const favoriteCount = await Favorite.getProductFavoriteCount(productId);

        res.json({
            success: true,
            message: '收藏成功',
            data: {
                favorite,
                favoriteCount
            }
        });

    } catch (error) {
        console.error('添加收藏错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 取消收藏
router.delete('/:productId', authenticateToken, async (req, res) => {
    try {
        const productId = parseInt(req.params.productId);
        const userId = req.user.id;

        if (isNaN(productId)) {
            return res.status(400).json({
                success: false,
                message: '无效的商品ID'
            });
        }

        // 取消收藏
        const removedFavorite = await Favorite.remove(userId, productId);
        
        if (!removedFavorite) {
            return res.status(404).json({
                success: false,
                message: '收藏记录不存在'
            });
        }

        // 获取更新后的收藏数量
        const favoriteCount = await Favorite.getProductFavoriteCount(productId);

        res.json({
            success: true,
            message: '取消收藏成功',
            data: {
                favoriteCount
            }
        });

    } catch (error) {
        console.error('取消收藏错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取用户的收藏列表
router.get('/my-favorites', authenticateToken, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 12;
        const userId = req.user.id;

        const result = await Favorite.getUserFavorites(userId, page, limit);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('获取收藏列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 检查商品收藏状态
router.get('/status/:productId', authenticateToken, async (req, res) => {
    try {
        const productId = parseInt(req.params.productId);
        const userId = req.user.id;

        if (isNaN(productId)) {
            return res.status(400).json({
                success: false,
                message: '无效的商品ID'
            });
        }

        const isFavorited = await Favorite.isFavorited(userId, productId);
        const favoriteCount = await Favorite.getProductFavoriteCount(productId);

        res.json({
            success: true,
            data: {
                isFavorited,
                favoriteCount
            }
        });

    } catch (error) {
        console.error('检查收藏状态错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 批量检查商品收藏状态
router.post('/status/batch', authenticateToken, async (req, res) => {
    try {
        const { productIds } = req.body;
        const userId = req.user.id;

        if (!Array.isArray(productIds) || productIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: '商品ID列表不能为空'
            });
        }

        // 验证所有ID都是数字
        const validProductIds = productIds.filter(id => !isNaN(parseInt(id))).map(id => parseInt(id));
        
        if (validProductIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有有效的商品ID'
            });
        }

        const favoriteStatus = await Favorite.getProductsFavoriteStatus(userId, validProductIds);

        res.json({
            success: true,
            data: favoriteStatus
        });

    } catch (error) {
        console.error('批量检查收藏状态错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取商品的收藏数量
router.get('/count/:productId', async (req, res) => {
    try {
        const productId = parseInt(req.params.productId);

        if (isNaN(productId)) {
            return res.status(400).json({
                success: false,
                message: '无效的商品ID'
            });
        }

        const favoriteCount = await Favorite.getProductFavoriteCount(productId);

        res.json({
            success: true,
            data: {
                favoriteCount
            }
        });

    } catch (error) {
        console.error('获取收藏数量错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取用户收藏统计
router.get('/stats', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const stats = await Favorite.getUserFavoriteStats(userId);

        res.json({
            success: true,
            data: stats
        });

    } catch (error) {
        console.error('获取收藏统计错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取商品的收藏用户列表
router.get('/users/:productId', async (req, res) => {
    try {
        const productId = parseInt(req.params.productId);
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;

        if (isNaN(productId)) {
            return res.status(400).json({
                success: false,
                message: '无效的商品ID'
            });
        }

        const users = await Favorite.getProductFavoriteUsers(productId, page, limit);

        res.json({
            success: true,
            data: {
                users
            }
        });

    } catch (error) {
        console.error('获取收藏用户列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;
